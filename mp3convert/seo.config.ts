// SEO配置文件
export const seoConfig = {
  baseUrl: 'https://www.tomp3.online',
  siteName: 'ToMP3',
  title: 'Free Audio & Video to MP3 Converter Online | FLAC, M4A, MOV, MP4 to MP3 | ToMP3',
  description: 'Convert FLAC to MP3, M4A to MP3, MOV to MP3, and MP4 to MP3 online for free. High-quality conversion with no upload required. 100% secure browser-based conversion.',
  keywords: 'flac to mp3, m4a to mp3, mov to mp3, mp4 to mp3, flac to mp3 converter, m4a to mp3 converter, mov to mp3 converter, mp4 to mp3 converter, audio converter, video to mp3, online converter, free converter, browser converter, no upload, secure conversion, high quality mp3, mp3convert',
  author: 'ToMP3',
  twitterHandle: '@tomp3online',
  ogImage: '/og-image.jpg',
  
  // 页面特定的SEO配置
  pages: {
    home: {
      title: 'Free Audio & Video to MP3 Converter Online | FLAC, M4A, MOV, MP4 to MP3 | ToMP3',
      description: 'Convert FLAC to MP3, M4A to MP3, MOV to MP3, and MP4 to MP3 online for free. High-quality conversion with no upload required. 100% secure browser-based conversion.',
      keywords: 'flac to mp3, m4a to mp3, mov to mp3, mp4 to mp3, audio converter, video to mp3, online converter, free converter'
    },
    flacToMp3: {
      title: 'FLAC to MP3 Converter - Free Online Conversion | ToMP3',
      description: 'Convert FLAC to MP3 online for free. High-quality conversion with no file uploads. Maintain audio quality while reducing file size.',
      keywords: 'flac to mp3, flac converter, lossless to mp3, audio converter'
    },
    m4aToMp3: {
      title: 'M4A to MP3 Converter - Free iTunes to MP3 | ToMP3',
      description: 'Convert M4A to MP3 online for free. Perfect for converting iTunes music files to universal MP3 format. No uploads required.',
      keywords: 'm4a to mp3, itunes converter, m4a converter, audio converter'
    },
    movToMp3: {
      title: 'MOV to MP3 Converter - Extract Audio from QuickTime | ToMP3',
      description: 'Convert MOV to MP3 online for free. Extract high-quality audio from QuickTime MOV video files. Browser-based conversion.',
      keywords: 'mov to mp3, quicktime converter, video to audio, mov audio extractor'
    },
    mp4ToMp3: {
      title: 'MP4 to MP3 Converter - Free Video to Audio | ToMP3',
      description: 'Convert MP4 to MP3 online for free. Extract audio from MP4 videos with high quality. No file uploads needed.',
      keywords: 'mp4 to mp3, video to audio, mp4 converter, audio extractor'
    },
    wavToMp3: {
      title: 'WAV to MP3 Converter - Compress Audio Files | ToMP3',
      description: 'Convert WAV to MP3 online for free. Reduce file size by 90% while maintaining good audio quality. Browser-based conversion.',
      keywords: 'wav to mp3, wav converter, audio compression, uncompressed to mp3'
    },
    aacToMp3: {
      title: 'AAC to MP3 Converter - Advanced Audio Codec | ToMP3',
      description: 'Convert AAC to MP3 online for free. Transform Advanced Audio Codec files to universal MP3 format for maximum compatibility.',
      keywords: 'aac to mp3, aac converter, advanced audio codec, audio converter'
    },
    oggToMp3: {
      title: 'OGG to MP3 Converter - Free Vorbis to MP3 | ToMP3',
      description: 'Convert OGG to MP3 online for free. Transform OGG Vorbis files to universal MP3 format. No uploads required.',
      keywords: 'ogg to mp3, ogg converter, vorbis to mp3, audio converter'
    },
    wmaToMp3: {
      title: 'WMA to MP3 Converter - Free Windows Media Audio Conversion | ToMP3',
      description: 'Convert WMA to MP3 online for free. Transform Windows Media Audio files to universal MP3 format with no upload required.',
      keywords: 'wma to mp3, wma converter, windows media audio to mp3, audio converter, free wma converter'
    },
    webmToMp3: {
      title: 'WEBM to MP3 Converter - Extract Audio from WEBM Videos | ToMP3',
      description: 'Convert WEBM to MP3 online for free. Extract audio from WEBM video files including YouTube videos. Browser-based conversion.',
      keywords: 'webm to mp3, webm converter, youtube webm to mp3, video to audio, extract audio from webm'
    },
    aviToMp3: {
      title: 'AVI to MP3 Converter - Extract Audio from AVI Videos | ToMP3',
      description: 'Convert AVI to MP3 online for free. Extract audio from AVI video files with high quality. No upload required.',
      keywords: 'avi to mp3, avi converter, avi to audio, video to mp3, extract audio from avi'
    },
    mkvToMp3: {
      title: 'MKV to MP3 Converter - Extract Audio from MKV Videos | ToMP3',
      description: 'Convert MKV to MP3 online for free. Extract audio from MKV (Matroska) video files including HD and 4K videos.',
      keywords: 'mkv to mp3, mkv converter, matroska to mp3, hd video to mp3, 4k video to mp3'
    },
    tgpToMp3: {
      title: '3GP to MP3 Converter - Extract Audio from 3GP Videos | ToMP3',
      description: 'Convert 3GP to MP3 online for free. Extract audio from 3GP (3GPP) video files including mobile phone videos.',
      keywords: '3gp to mp3, 3gp converter, 3gpp to mp3, mobile video to mp3, phone video converter'
    },
    opusToMp3: {
      title: 'OPUS to MP3 Converter - Convert OPUS Audio to MP3 | ToMP3',
      description: 'Convert OPUS to MP3 online for free. Transform OPUS audio files to universal MP3 format with no upload required.',
      keywords: 'opus to mp3, opus converter, opus audio to mp3, audio converter, free opus converter'
    },
    apeToMp3: {
      title: 'APE to MP3 Converter - Convert Monkey\'s Audio to MP3 | ToMP3',
      description: 'Convert APE to MP3 online for free. Transform Monkey\'s Audio files to universal MP3 format with no upload required.',
      keywords: 'ape to mp3, ape converter, monkeys audio to mp3, lossless audio converter, free ape converter'
    },
    audioFormatsGuide: {
      title: 'Audio Formats Guide - MP3, FLAC, WAV, AAC, WMA, OPUS, APE | ToMP3',
      description: 'Complete guide to audio formats including MP3, FLAC, WAV, AAC, WMA, OPUS, and APE. Learn about compression, quality, and when to use each format.',
      keywords: 'audio formats guide, mp3 vs flac, wav vs mp3, audio compression, lossless vs lossy, audio format comparison'
    },
    help: {
      title: 'Help & FAQ - ToMP3 Audio Converter Guide',
      description: 'Learn how to use ToMP3 audio converter. Get help with FLAC, M4A, MOV, MP4, WAV, AAC, OGG, WMA, and WEBM to MP3 conversion.',
      keywords: 'audio converter help, conversion guide, mp3 converter faq, tomp3 help'
    },
    audioVideoFormatsComparison: {
      title: 'Audio & Video Formats Comparison 2024: MP3 vs FLAC vs WAV vs AAC vs WMA',
      description: 'Complete comparison guide of audio and video formats. Compare MP3, FLAC, WAV, AAC, WMA, OPUS, APE for audio and MP4, MOV, AVI, MKV, WEBM, 3GP for video. Learn quality, compression, and compatibility differences.',
      keywords: 'audio video formats comparison, MP3 vs FLAC vs WAV, AAC vs MP3, video format comparison, MP4 vs MKV, WEBM to MP3, audio quality comparison, video to audio converter, format converter guide'
    },
    howToConvertAudioVideoToMp3: {
      title: 'How to Convert Audio & Video to MP3 - Complete Guide 2024',
      description: 'Step-by-step guide on how to convert FLAC, WAV, M4A, AAC, WMA to MP3 and extract audio from MP4, MOV, AVI, MKV, WEBM videos. Free online conversion tutorials.',
      keywords: 'how to convert to MP3, convert FLAC to MP3, extract audio from video, WAV to MP3 tutorial, MP4 to MP3 guide, convert audio files, video to MP3 converter, audio conversion guide'
    }
  }
};

export default seoConfig;
