"use client";

import React from "react";
import Link from "next/link";

export default function HowToConvertGuide() {
  // Structured Data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "How to Convert Audio and Video Files to MP3 - Complete Guide 2024",
    "description": "Step-by-step guide on how to convert FLAC, WAV, M4A, AAC, WMA, OPUS, APE to MP3 and extract audio from MP4, MOV, AVI, MKV, WEBM, 3GP videos. Free online conversion tutorials.",
    "image": "https://www.tomp3.online/og-image.jpg",
    "totalTime": "PT2M",
    "estimatedCost": {
      "@type": "MonetaryAmount",
      "currency": "USD",
      "value": "0"
    },
    "supply": [],
    "tool": [
      {
        "@type": "HowToTool",
        "name": "Web Browser"
      }
    ],
    "step": [
      {
        "@type": "HowToStep",
        "name": "Select converter",
        "text": "Choose the appropriate converter for your file format",
        "url": "https://www.tomp3.online"
      },
      {
        "@type": "HowToStep",
        "name": "Upload file",
        "text": "Drag and drop or click to upload your audio or video file",
        "url": "https://www.tomp3.online"
      },
      {
        "@type": "HowToStep",
        "name": "Convert",
        "text": "Click the convert button to start the conversion process",
        "url": "https://www.tomp3.online"
      },
      {
        "@type": "HowToStep",
        "name": "Download",
        "text": "Download your converted MP3 file",
        "url": "https://www.tomp3.online"
      }
    ]
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-green-50 to-emerald-50">
        {/* Navigation Bar */}
        <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  ToMP3
                </span>
              </Link>
              <div className="hidden sm:flex items-center space-x-6">
                <Link href="/audio-video-formats-comparison" className="text-gray-600 hover:text-green-600 transition-colors">Format Guide</Link>
                <Link href="/audio-formats-guide" className="text-gray-600 hover:text-green-600 transition-colors">Audio Guide</Link>
                <Link href="/help" className="text-gray-600 hover:text-green-600 transition-colors">Help</Link>
                <Link href="/" className="text-gray-600 hover:text-green-600 transition-colors">All Converters</Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              How to Convert Audio & Video to MP3
              <span className="block bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                Complete Step-by-Step Guide 2024
              </span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Learn <strong>how to convert FLAC to MP3</strong>, <strong>extract audio from MP4</strong>, <strong>convert WAV files to MP3</strong>, and more. Complete tutorials for all audio and video formats.
            </p>
          </div>

          {/* Quick Answer Box */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Quick Answer: How to Convert to MP3</h2>
            <ol className="space-y-3">
              <li className="flex items-start">
                <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">1</span>
                <div>
                  <strong>Choose your converter:</strong> Select the appropriate converter for your file format (e.g., <Link href="/flac-to-mp3" className="text-green-600 hover:text-green-800">FLAC to MP3</Link> for FLAC files)
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">2</span>
                <div>
                  <strong>Upload your file:</strong> Drag and drop or click to select your audio/video file
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">3</span>
                <div>
                  <strong>Convert:</strong> Click the &quot;Convert to MP3&quot; button
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">4</span>
                <div>
                  <strong>Download:</strong> Save your converted MP3 file
                </div>
              </li>
            </ol>
            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-gray-700">
                <strong>⚡ Pro tip:</strong> All conversions happen in your browser - no uploads, no waiting, 100% private!
              </p>
            </div>
          </div>

          {/* Detailed Guides by Format */}
          <div className="space-y-8 mb-12">
            {/* How to Convert FLAC to MP3 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">How to Convert FLAC to MP3 (Lossless to Lossy)</h2>
              <p className="text-gray-600 mb-4">
                FLAC files are large but perfect quality. Here&apos;s how to convert FLAC to MP3 for smaller files:
              </p>
              <ol className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="text-blue-600 font-bold mr-2">1.</span>
                  <span>Go to our <Link href="/flac-to-mp3" className="text-blue-600 hover:text-blue-800 font-semibold">FLAC to MP3 converter</Link></span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 font-bold mr-2">2.</span>
                  <span>Upload your FLAC file (supports files up to 2GB)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 font-bold mr-2">3.</span>
                  <span>The converter automatically uses 320kbps for best quality</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 font-bold mr-2">4.</span>
                  <span>Click &quot;Convert FLAC to MP3&quot; and wait a few seconds</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 font-bold mr-2">5.</span>
                  <span>Download your MP3 file (90% smaller than FLAC!)</span>
                </li>
              </ol>
              <div className="p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>Why convert FLAC to MP3?</strong> FLAC files are typically 25-30MB per song, while MP3 is only 5-8MB with minimal quality loss.
                </p>
              </div>
            </div>

            {/* How to Extract Audio from MP4 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">How to Extract Audio from MP4 Videos</h2>
              <p className="text-gray-600 mb-4">
                Want to extract audio from MP4 videos? Convert MP4 to MP3 in seconds:
              </p>
              <ol className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="text-orange-600 font-bold mr-2">1.</span>
                  <span>Open our <Link href="/mp4-to-mp3" className="text-orange-600 hover:text-orange-800 font-semibold">MP4 to MP3 converter</Link></span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 font-bold mr-2">2.</span>
                  <span>Select your MP4 video file</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 font-bold mr-2">3.</span>
                  <span>The tool extracts the audio track automatically</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 font-bold mr-2">4.</span>
                  <span>Click &quot;Convert MP4 to MP3&quot;</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-600 font-bold mr-2">5.</span>
                  <span>Download the extracted MP3 audio</span>
                </li>
              </ol>
              <div className="p-4 bg-orange-50 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>Perfect for:</strong> Music videos, podcasts, lectures, YouTube downloads, and any MP4 content.
                </p>
              </div>
            </div>

            {/* How to Convert WAV to MP3 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">How to Convert WAV to MP3 (Reduce File Size by 90%)</h2>
              <p className="text-gray-600 mb-4">
                WAV files are uncompressed and huge. Here&apos;s how to convert WAV to MP3:
              </p>
              <ol className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="text-purple-600 font-bold mr-2">1.</span>
                  <span>Visit our <Link href="/wav-to-mp3" className="text-purple-600 hover:text-purple-800 font-semibold">WAV to MP3 converter</Link></span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-600 font-bold mr-2">2.</span>
                  <span>Upload your WAV file (can be very large)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-600 font-bold mr-2">3.</span>
                  <span>Choose quality (recommend 320kbps for music)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-600 font-bold mr-2">4.</span>
                  <span>Click &quot;Convert WAV to MP3&quot;</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-600 font-bold mr-2">5.</span>
                  <span>Download your compressed MP3 (10x smaller!)</span>
                </li>
              </ol>
              <div className="p-4 bg-purple-50 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>File size example:</strong> 50MB WAV file → 5MB MP3 file (90% reduction!)
                </p>
              </div>
            </div>

            {/* How to Convert YouTube WEBM to MP3 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">How to Convert YouTube WEBM to MP3</h2>
              <p className="text-gray-600 mb-4">
                Downloaded a WEBM video from YouTube? Extract the audio easily:
              </p>
              <ol className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="text-teal-600 font-bold mr-2">1.</span>
                  <span>Go to our <Link href="/webm-to-mp3" className="text-teal-600 hover:text-teal-800 font-semibold">WEBM to MP3 converter</Link></span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-600 font-bold mr-2">2.</span>
                  <span>Upload your WEBM video file</span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-600 font-bold mr-2">3.</span>
                  <span>The converter extracts the Opus/Vorbis audio</span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-600 font-bold mr-2">4.</span>
                  <span>Click &quot;Convert WEBM to MP3&quot;</span>
                </li>
                <li className="flex items-start">
                  <span className="text-teal-600 font-bold mr-2">5.</span>
                  <span>Download your MP3 audio file</span>
                </li>
              </ol>
              <div className="p-4 bg-teal-50 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>Note:</strong> WEBM is YouTube&apos;s preferred format for many videos, especially those with VP9 codec.
                </p>
              </div>
            </div>

            {/* How to Convert M4A to MP3 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">How to Convert iTunes M4A to MP3</h2>
              <p className="text-gray-600 mb-4">
                Have iTunes M4A files? Convert them to universal MP3:
              </p>
              <ol className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="text-green-600 font-bold mr-2">1.</span>
                  <span>Open our <Link href="/m4a-to-mp3" className="text-green-600 hover:text-green-800 font-semibold">M4A to MP3 converter</Link></span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 font-bold mr-2">2.</span>
                  <span>Select your M4A file from iTunes or elsewhere</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 font-bold mr-2">3.</span>
                  <span>The converter handles AAC codec automatically</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 font-bold mr-2">4.</span>
                  <span>Click &quot;Convert M4A to MP3&quot;</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 font-bold mr-2">5.</span>
                  <span>Download your compatible MP3 file</span>
                </li>
              </ol>
              <div className="p-4 bg-green-50 rounded-lg">
                <p className="text-sm text-gray-700">
                  <strong>Why convert?</strong> M4A works on Apple devices, but MP3 works everywhere.
                </p>
              </div>
            </div>
          </div>

          {/* Common Conversion Scenarios */}
          <div className="bg-gradient-to-r from-gray-50 to-green-50 rounded-2xl p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Common Conversion Scenarios & Solutions</h2>
            
            <div className="space-y-4">
              <div className="bg-white rounded-xl p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  &quot;I need to convert my entire music library to MP3&quot;
                </h3>
                <p className="text-gray-600 mb-3">
                  If you have a mixed library of FLAC, WAV, and M4A files, convert them all to MP3 for universal compatibility:
                </p>
                <div className="flex flex-wrap gap-2">
                  <Link href="/flac-to-mp3" className="text-sm bg-blue-100 text-blue-700 px-3 py-1 rounded-full hover:bg-blue-200">FLAC → MP3</Link>
                  <Link href="/wav-to-mp3" className="text-sm bg-purple-100 text-purple-700 px-3 py-1 rounded-full hover:bg-purple-200">WAV → MP3</Link>
                  <Link href="/m4a-to-mp3" className="text-sm bg-green-100 text-green-700 px-3 py-1 rounded-full hover:bg-green-200">M4A → MP3</Link>
                  <Link href="/aac-to-mp3" className="text-sm bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full hover:bg-yellow-200">AAC → MP3</Link>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  &quot;I want to extract audio from videos for my podcast&quot;
                </h3>
                <p className="text-gray-600 mb-3">
                  Extract audio from any video format for podcast editing:
                </p>
                <div className="flex flex-wrap gap-2">
                  <Link href="/mp4-to-mp3" className="text-sm bg-orange-100 text-orange-700 px-3 py-1 rounded-full hover:bg-orange-200">MP4 → MP3</Link>
                  <Link href="/mov-to-mp3" className="text-sm bg-pink-100 text-pink-700 px-3 py-1 rounded-full hover:bg-pink-200">MOV → MP3</Link>
                  <Link href="/webm-to-mp3" className="text-sm bg-teal-100 text-teal-700 px-3 py-1 rounded-full hover:bg-teal-200">WEBM → MP3</Link>
                  <Link href="/mkv-to-mp3" className="text-sm bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full hover:bg-indigo-200">MKV → MP3</Link>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  &quot;My car stereo only plays MP3 files&quot;
                </h3>
                <p className="text-gray-600 mb-3">
                  Convert any audio format to MP3 for car compatibility:
                </p>
                <div className="flex flex-wrap gap-2">
                  <Link href="/wma-to-mp3" className="text-sm bg-red-100 text-red-700 px-3 py-1 rounded-full hover:bg-red-200">WMA → MP3</Link>
                  <Link href="/ogg-to-mp3" className="text-sm bg-pink-100 text-pink-700 px-3 py-1 rounded-full hover:bg-pink-200">OGG → MP3</Link>
                  <Link href="/opus-to-mp3" className="text-sm bg-violet-100 text-violet-700 px-3 py-1 rounded-full hover:bg-violet-200">OPUS → MP3</Link>
                  <Link href="/ape-to-mp3" className="text-sm bg-rose-100 text-rose-700 px-3 py-1 rounded-full hover:bg-rose-200">APE → MP3</Link>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  &quot;I have old phone videos in 3GP format&quot;
                </h3>
                <p className="text-gray-600 mb-3">
                  Extract audio from old mobile phone videos:
                </p>
                <div className="flex flex-wrap gap-2">
                  <Link href="/3gp-to-mp3" className="text-sm bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full hover:bg-emerald-200">3GP → MP3</Link>
                  <Link href="/avi-to-mp3" className="text-sm bg-amber-100 text-amber-700 px-3 py-1 rounded-full hover:bg-amber-200">AVI → MP3</Link>
                </div>
              </div>
            </div>
          </div>

          {/* Tips and Best Practices */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Pro Tips for Converting to MP3</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border-l-4 border-green-500 pl-4">
                <h3 className="font-semibold text-gray-900 mb-2">🎵 Audio Quality Settings</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• <strong>320 kbps:</strong> Best quality for music</li>
                  <li>• <strong>192 kbps:</strong> Good balance of quality/size</li>
                  <li>• <strong>128 kbps:</strong> Acceptable for speech/podcasts</li>
                  <li>• <strong>96 kbps:</strong> Minimum for audiobooks</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-blue-500 pl-4">
                <h3 className="font-semibold text-gray-900 mb-2">📁 File Size Expectations</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• <strong>WAV:</strong> 10MB per minute</li>
                  <li>• <strong>FLAC:</strong> 5MB per minute</li>
                  <li>• <strong>MP3 320k:</strong> 2.5MB per minute</li>
                  <li>• <strong>MP3 128k:</strong> 1MB per minute</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-purple-500 pl-4">
                <h3 className="font-semibold text-gray-900 mb-2">⚡ Conversion Speed</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• Audio files: 2-5 seconds</li>
                  <li>• Short videos: 5-10 seconds</li>
                  <li>• Long videos: 10-30 seconds</li>
                  <li>• No upload time needed!</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-orange-500 pl-4">
                <h3 className="font-semibold text-gray-900 mb-2">🔒 Privacy & Security</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• Files stay on your device</li>
                  <li>• No server uploads</li>
                  <li>• Browser-based conversion</li>
                  <li>• 100% private and secure</li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            
            <div className="space-y-4">
              <details className="bg-white rounded-lg p-4">
                <summary className="font-semibold text-gray-900 cursor-pointer">How long does it take to convert to MP3?</summary>
                <p className="text-gray-600 mt-2">
                  Most conversions take just 2-10 seconds. Large video files may take up to 30 seconds. Since everything happens in your browser, there&apos;s no upload time!
                </p>
              </details>
              
              <details className="bg-white rounded-lg p-4">
                <summary className="font-semibold text-gray-900 cursor-pointer">What&apos;s the maximum file size I can convert?</summary>
                <p className="text-gray-600 mt-2">
                  Our converters can handle files up to 2GB. This covers 99% of audio files and most video files. For larger files, consider splitting them first.
                </p>
              </details>
              
              <details className="bg-white rounded-lg p-4">
                <summary className="font-semibold text-gray-900 cursor-pointer">Will I lose quality when converting to MP3?</summary>
                <p className="text-gray-600 mt-2">
                  MP3 is a lossy format, but at 320kbps, most people can&apos;t hear the difference. For archiving, keep the original. For listening, MP3 is perfect.
                </p>
              </details>
              
              <details className="bg-white rounded-lg p-4">
                <summary className="font-semibold text-gray-900 cursor-pointer">Can I convert multiple files at once?</summary>
                <p className="text-gray-600 mt-2">
                  Currently, we support one file at a time for optimal performance. You can convert files sequentially - each conversion only takes seconds!
                </p>
              </details>
              
              <details className="bg-white rounded-lg p-4">
                <summary className="font-semibold text-gray-900 cursor-pointer">Do you store my files?</summary>
                <p className="text-gray-600 mt-2">
                  No! All conversions happen in your browser. Your files never leave your device and we don&apos;t store anything. It&apos;s 100% private and secure.
                </p>
              </details>
            </div>
          </div>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Convert Your Files?</h2>
            <p className="text-xl mb-8 text-green-100">
              Choose your format and start converting - it&apos;s free, fast, and secure!
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/" className="bg-white text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors">
                Browse All Converters
              </Link>
              <Link href="/audio-video-formats-comparison" className="bg-green-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-800 transition-colors">
                Compare Formats
              </Link>
              <Link href="/audio-formats-guide" className="bg-green-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-800 transition-colors">
                Audio Guide
              </Link>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}