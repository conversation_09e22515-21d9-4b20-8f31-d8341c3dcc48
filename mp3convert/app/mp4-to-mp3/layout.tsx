import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "MP4 to MP3 Converter Free Online | Extract Audio from Videos | ToMP3",
  description: "Convert MP4 to MP3 online for free. Extract high-quality audio from MP4 videos. Most popular video format to MP3 conversion. No upload required - 100% secure and private.",
  keywords: "mp4 to mp3, mp4 to mp3 converter, convert mp4 to mp3, mp4 to mp3 online, free mp4 to mp3, video to mp3, extract audio from mp4, mp4 audio extraction",
  openGraph: {
    title: "MP4 to MP3 Converter Free Online | ToMP3",
    description: "Convert MP4 to MP3 online for free. Extract audio from the world's most popular video format with no upload required.",
    url: "https://www.tomp3.online/mp4-to-mp3",
    type: "website",
  },
  twitter: {
    title: "MP4 to MP3 Converter Free Online | ToMP3",
    description: "Convert MP4 to MP3 online for free. Extract audio from videos.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/mp4-to-mp3",
  },
};

export default function Mp4ToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
