import Link from "next/link";

export default function Home() {
  // Structured Data for SEO - WebApplication + FAQ
  const webAppStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "FLAC to MP3, M4A to MP3, MOV to MP3, MP4 to MP3 Converter - ToMP3",
    "description": "Convert FLAC to MP3, M4A to MP3, MOV to MP3, and MP4 to MP3 online for free. High-quality conversion with no upload required.",
    "url": "https://www.tomp3.online",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "FLAC to MP3 conversion",
      "M4A to MP3 conversion",
      "MOV to MP3 conversion",
      "MP4 to MP3 conversion",
      "WAV to MP3 conversion",
      "AAC to MP3 conversion",
      "OGG to MP3 conversion",
      "High-quality audio output",
      "No file upload required",
      "100% secure and private",
      "Free unlimited conversions",
      "Browser-based conversion",
      "WebAssembly powered"
    ],
    "browserRequirements": "Requires JavaScript and WebAssembly support",
    "softwareVersion": "1.0",
    "author": {
      "@type": "Organization",
      "name": "ToMP3"
    }
  };

  // FAQ Structured Data for better SEO
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How do I convert FLAC to MP3 for free?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Simply visit our FLAC to MP3 converter page, drag and drop your FLAC file, and click convert. The conversion happens in your browser with no upload required."
        }
      },
      {
        "@type": "Question",
        "name": "Is this MP3 converter completely free?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes! Our audio and video to MP3 converter is completely free with no hidden costs, registration requirements, or file limits. Convert unlimited files."
        }
      },
      {
        "@type": "Question",
        "name": "Are my files secure when converting to MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Absolutely! All conversion happens directly in your browser using WebAssembly technology. Your files never leave your device, ensuring complete privacy and security."
        }
      },
      {
        "@type": "Question",
        "name": "What audio and video formats can I convert to MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "We support FLAC, M4A, WAV, AAC, OGG audio formats and MP4, MOV video formats for conversion to high-quality MP3 files."
        }
      },
      {
        "@type": "Question",
        "name": "Do I need to download software to convert files to MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "No downloads required! Our converter works entirely in your web browser. Just visit our website and start converting immediately."
        }
      }
    ]
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webAppStructuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Navigation Bar */}
        <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  ToMP3
                </span>
              </Link>
              <div className="hidden lg:flex items-center space-x-3">
                              <Link href="/flac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">FLAC</Link>
                              <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">M4A</Link>
                              <Link href="/wav-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">WAV</Link>
                              <Link href="/aac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">AAC</Link>
                              <Link href="/ogg-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">OGG</Link>
                              <Link href="/wma-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">WMA</Link>
                              <Link href="/webm-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">WEBM</Link>
                              <Link href="/avi-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">AVI</Link>
                              <Link href="/mkv-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">MKV</Link>
                              <Link href="/3gp-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">3GP</Link>
                              <Link href="/opus-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">OPUS</Link>
                              <Link href="/ape-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">APE</Link>
                              <Link href="/mov-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">MOV</Link>
                              <Link href="/mp4-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">MP4</Link>
                              <Link href="/audio-formats-guide" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">Audio Guide</Link>
                              <Link href="/help" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">Help</Link>
                            </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Free Online Audio & Video
              <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                to MP3 Converter
              </span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Convert <strong>FLAC to MP3</strong>, <strong>M4A to MP3</strong>, <strong>WAV to MP3</strong>, <strong>AAC to MP3</strong>, <strong>OGG to MP3</strong>, <strong>MOV to MP3</strong>, and <strong>MP4 to MP3</strong> online for free.
              <span className="font-semibold text-gray-800">High-quality conversion with no uploads required - 100% secure and private browser-based conversion.</span>
            </p>
            
            {/* Long-tail keyword rich description */}
            <div className="mt-6 text-base text-gray-600 max-w-4xl mx-auto">
              <p className="mb-3">
                Looking for a <strong>free audio converter online</strong>? Our <strong>browser-based MP3 converter</strong> supports all popular formats including <strong>lossless FLAC to MP3</strong>, <strong>iTunes M4A to MP3</strong>, <strong>uncompressed WAV to MP3</strong>, and <strong>video to audio conversion</strong> from MP4 and MOV files.
              </p>
              <p>
                No software download required - convert <strong>audio files to MP3</strong> directly in your browser with <strong>WebAssembly technology</strong> for fast, secure, and private conversions.
              </p>
            </div>
            
            {/* Quick Access Buttons */}
            <div className="flex flex-wrap justify-center gap-3 mt-8">
              <Link href="/flac-to-mp3" className="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                FLAC to MP3
              </Link>
              <Link href="/m4a-to-mp3" className="inline-flex items-center px-4 py-2 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                M4A to MP3
              </Link>
              <Link href="/wav-to-mp3" className="inline-flex items-center px-4 py-2 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                WAV to MP3
              </Link>
              <Link href="/aac-to-mp3" className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white font-semibold rounded-lg hover:bg-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                AAC to MP3
              </Link>
              <Link href="/ogg-to-mp3" className="inline-flex items-center px-4 py-2 bg-pink-600 text-white font-semibold rounded-lg hover:bg-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                OGG to MP3
              </Link>
              <Link href="/wma-to-mp3" className="inline-flex items-center px-4 py-2 bg-red-600 text-white font-semibold rounded-lg hover:bg-red-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                WMA to MP3
              </Link>
              <Link href="/mov-to-mp3" className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                MOV to MP3
              </Link>
              <Link href="/mp4-to-mp3" className="inline-flex items-center px-4 py-2 bg-orange-600 text-white font-semibold rounded-lg hover:bg-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                MP4 to MP3
              </Link>
              <Link href="/webm-to-mp3" className="inline-flex items-center px-4 py-2 bg-teal-600 text-white font-semibold rounded-lg hover:bg-teal-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                WEBM to MP3
              </Link>
              <Link href="/avi-to-mp3" className="inline-flex items-center px-4 py-2 bg-amber-600 text-white font-semibold rounded-lg hover:bg-amber-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                AVI to MP3
              </Link>
              <Link href="/mkv-to-mp3" className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                MKV to MP3
              </Link>
              <Link href="/3gp-to-mp3" className="inline-flex items-center px-4 py-2 bg-emerald-600 text-white font-semibold rounded-lg hover:bg-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                3GP to MP3
              </Link>
              <Link href="/opus-to-mp3" className="inline-flex items-center px-4 py-2 bg-violet-600 text-white font-semibold rounded-lg hover:bg-violet-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                OPUS to MP3
              </Link>
              <Link href="/ape-to-mp3" className="inline-flex items-center px-4 py-2 bg-rose-600 text-white font-semibold rounded-lg hover:bg-rose-700 transition-all duration-200 shadow-lg hover:shadow-xl text-sm">
                APE to MP3
              </Link>
            </div>
          </div>

          {/* Format Converter Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {/* FLAC to MP3 */}
            <Link href="/flac-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">FLAC to MP3</h3>
                  <p className="text-blue-100 text-sm">Convert lossless FLAC files to MP3 format</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">High-quality conversion from FLAC to MP3 while maintaining excellent audio quality.</p>
                </div>
              </div>
            </Link>

            {/* M4A to MP3 */}
            <Link href="/m4a-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">M4A to MP3</h3>
                  <p className="text-green-100 text-sm">Convert iTunes M4A files to MP3 format</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Perfect for converting iTunes music files to universal MP3 format.</p>
                </div>
              </div>
            </Link>

            {/* MOV to MP3 */}
            <Link href="/mov-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">MOV to MP3</h3>
                  <p className="text-purple-100 text-sm">Extract audio from QuickTime MOV videos</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Extract high-quality audio from MOV video files as MP3.</p>
                </div>
              </div>
            </Link>

            {/* MP4 to MP3 */}
            <Link href="/mp4-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-orange-500 to-red-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">MP4 to MP3</h3>
                  <p className="text-orange-100 text-sm">Extract audio from MP4 videos</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Convert the world&apos;s most popular video format to MP3 audio.</p>
                </div>
              </div>
            </Link>

            {/* WAV to MP3 */}
            <Link href="/wav-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">WAV to MP3</h3>
                  <p className="text-purple-100 text-sm">Convert uncompressed WAV files to MP3</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Reduce file size by 90% while maintaining good audio quality.</p>
                </div>
              </div>
            </Link>

            {/* AAC to MP3 */}
            <Link href="/aac-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-yellow-500 to-orange-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">AAC to MP3</h3>
                  <p className="text-yellow-100 text-sm">Convert Advanced Audio Codec to MP3</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Transform AAC files to universal MP3 format for maximum compatibility.</p>
                </div>
              </div>
            </Link>

            {/* OGG to MP3 */}
            <Link href="/ogg-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-pink-500 to-rose-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">OGG to MP3</h3>
                  <p className="text-pink-100 text-sm">Convert OGG Vorbis to MP3</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Transform OGG Vorbis files to universal MP3 format.</p>
                </div>
              </div>
            </Link>

            {/* WMA to MP3 */}
            <Link href="/wma-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-red-500 to-orange-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">WMA to MP3</h3>
                  <p className="text-red-100 text-sm">Windows Media Audio to MP3</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Convert Windows Media Audio files to universal MP3 format.</p>
                </div>
              </div>
            </Link>

            {/* WEBM to MP3 */}
            <Link href="/webm-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-teal-500 to-cyan-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">WEBM to MP3</h3>
                  <p className="text-teal-100 text-sm">Extract audio from WEBM videos</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Extract audio from WEBM videos including YouTube content.</p>
                </div>
              </div>
            </Link>

            {/* AVI to MP3 */}
            <Link href="/avi-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-amber-500 to-yellow-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">AVI to MP3</h3>
                  <p className="text-amber-100 text-sm">Classic video to audio</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Extract audio from classic AVI video files.</p>
                </div>
              </div>
            </Link>

            {/* MKV to MP3 */}
            <Link href="/mkv-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">MKV to MP3</h3>
                  <p className="text-indigo-100 text-sm">HD video to audio</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Extract audio from HD and 4K MKV videos.</p>
                </div>
              </div>
            </Link>

            {/* 3GP to MP3 */}
            <Link href="/3gp-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-emerald-500 to-green-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">3GP to MP3</h3>
                  <p className="text-emerald-100 text-sm">Mobile video to audio</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Extract audio from 3GP mobile videos.</p>
                </div>
              </div>
            </Link>

            {/* OPUS to MP3 */}
            <Link href="/opus-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-violet-500 to-purple-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">OPUS to MP3</h3>
                  <p className="text-violet-100 text-sm">Modern audio to MP3</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Convert OPUS audio files to MP3.</p>
                </div>
              </div>
            </Link>

            {/* APE to MP3 */}
            <Link href="/ape-to-mp3" className="group">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300 group-hover:-translate-y-1">
                <div className="bg-gradient-to-r from-rose-500 to-pink-600 p-6 text-white">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold mb-2">APE to MP3</h3>
                  <p className="text-rose-100 text-sm">Lossless audio to MP3</p>
                </div>
                <div className="p-4">
                  <p className="text-gray-600 text-sm">Convert Monkey&apos;s Audio files to MP3.</p>
                </div>
              </div>
            </Link>
          </div>

          {/* Popular Format Conversions */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 sm:p-12 mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Popular Format Conversions</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Convert the most common audio and video formats to MP3 with high quality
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* FLAC to MP3 */}
              <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100 hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">FLAC to MP3</h3>
                <p className="text-gray-600 text-sm">Convert high-quality FLAC audio files to MP3 format while maintaining excellent sound quality</p>
              </div>

              {/* M4A to MP3 */}
              <div className="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border border-green-100 hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">M4A to MP3</h3>
                <p className="text-gray-600 text-sm">Convert M4A audio files from iTunes and other sources to universal MP3 format</p>
              </div>

              {/* MOV to MP3 */}
              <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl border border-purple-100 hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">MOV to MP3</h3>
                <p className="text-gray-600 text-sm">Extract audio from MOV video files and convert to MP3 for easy sharing and playback</p>
              </div>

              {/* MP4 to MP3 */}
              <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 hover:shadow-lg transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">MP4 to MP3</h3>
                <p className="text-gray-600 text-sm">Convert MP4 video files to MP3 audio format for music libraries and audio players</p>
              </div>
            </div>

            {/* Additional Benefits */}
            <div className="mt-12 text-center">
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Why Choose Our Free MP3 Converter?</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">🎵 Premium Audio Quality</h4>
                    <p className="text-gray-600 text-sm">Maintain excellent audio quality when converting FLAC to MP3, M4A to MP3, WAV to MP3, and video to MP3. Our advanced encoding preserves audio fidelity.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">🔒 100% Private & Secure</h4>
                    <p className="text-gray-600 text-sm">All conversions happen locally in your browser using WebAssembly - no file uploads to servers, complete privacy and security guaranteed.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">⚡ Instant Conversion</h4>
                    <p className="text-gray-600 text-sm">Convert audio and video files to MP3 in seconds with our optimized WebAssembly-powered conversion engine. No waiting, no queues.</p>
                  </div>
                </div>
              </div>
            </div>
  
            {/* FAQ Section */}
            <div className="mt-16">
              <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h3>
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-xl border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3">How do I convert FLAC to MP3 for free?</h4>
                  <p className="text-gray-600">Simply visit our FLAC to MP3 converter page, drag and drop your FLAC file, and click convert. The conversion happens in your browser with no upload required.</p>
                </div>
                
                <div className="bg-white p-6 rounded-xl border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3">Is this MP3 converter completely free?</h4>
                  <p className="text-gray-600">Yes! Our audio and video to MP3 converter is completely free with no hidden costs, registration requirements, or file limits. Convert unlimited files.</p>
                </div>
                
                <div className="bg-white p-6 rounded-xl border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3">What formats can I convert to MP3?</h4>
                  <p className="text-gray-600">We support FLAC, M4A, WAV, AAC, OGG audio formats and MP4, MOV video formats for conversion to high-quality MP3 files. All conversions maintain excellent audio quality.</p>
                </div>
                
                <div className="bg-white p-6 rounded-xl border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3">Do I need to download software to convert files?</h4>
                  <p className="text-gray-600">No downloads required! Our converter works entirely in your web browser using advanced WebAssembly technology. Just visit our website and start converting immediately.</p>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white border-t border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            {/* Logo and Description */}
            <div className="flex items-center justify-center mb-8">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                ToMP3
              </span>
            </div>
            
            {/* Footer Links */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 mb-8 text-center md:text-left justify-items-center md:justify-items-start">
              {/* Audio Converters */}
              <div className="text-center md:text-left">
                <h4 className="font-semibold text-gray-900 mb-4">Audio to MP3</h4>
                <ul className="space-y-2 text-sm text-center md:text-left">
                  <li><Link href="/flac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">FLAC to MP3</Link></li>
                  <li><Link href="/m4a-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">M4A to MP3</Link></li>
                  <li><Link href="/wav-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">WAV to MP3</Link></li>
                  <li><Link href="/aac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">AAC to MP3</Link></li>
                  <li><Link href="/ogg-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">OGG to MP3</Link></li>
                  <li><Link href="/wma-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">WMA to MP3</Link></li>
                  <li><Link href="/opus-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">OPUS to MP3</Link></li>
                  <li><Link href="/ape-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">APE to MP3</Link></li>
                </ul>
              </div>
              
              {/* Video Converters */}
              <div className="text-center md:text-left">
                <h4 className="font-semibold text-gray-900 mb-4">Video to MP3</h4>
                <ul className="space-y-2 text-sm text-center md:text-left">
                  <li><Link href="/mp4-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">MP4 to MP3</Link></li>
                  <li><Link href="/mov-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">MOV to MP3</Link></li>
                  <li><Link href="/avi-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">AVI to MP3</Link></li>
                  <li><Link href="/mkv-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">MKV to MP3</Link></li>
                  <li><Link href="/webm-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">WEBM to MP3</Link></li>
                  <li><Link href="/3gp-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">3GP to MP3</Link></li>
                </ul>
              </div>
              
              {/* Resources & Guides */}
              <div className="text-center md:text-left">
                <h4 className="font-semibold text-gray-900 mb-4">Resources</h4>
                <ul className="space-y-2 text-sm text-center md:text-left">
                  <li><Link href="/help" className="text-gray-600 hover:text-blue-600 transition-colors">Help & FAQ</Link></li>
                  <li><Link href="/audio-formats-guide" className="text-gray-600 hover:text-blue-600 transition-colors">Audio Formats Guide</Link></li>
                  <li><Link href="/audio-video-formats-comparison" className="text-gray-600 hover:text-blue-600 transition-colors">Format Comparison</Link></li>
                  <li><Link href="/how-to-convert-audio-video-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">How to Convert</Link></li>
                  <li><Link href="/privacy-policy" className="text-gray-600 hover:text-blue-600 transition-colors">Privacy Policy</Link></li>
                  <li><Link href="/terms-of-service" className="text-gray-600 hover:text-blue-600 transition-colors">Terms of Service</Link></li>
                </ul>
              </div>
              
              {/* Features */}
              <div className="text-center md:text-left">
                <h4 className="font-semibold text-gray-900 mb-4">Features</h4>
                <ul className="space-y-2 text-sm text-gray-600 text-center md:text-left">
                  <li>✓ Free & Unlimited</li>
                  <li>✓ No Upload Required</li>
                  <li>✓ High Quality Output</li>
                  <li>✓ Browser-Based</li>
                  <li>✓ 100% Private</li>
                </ul>
              </div>
            </div>
            
            <div className="border-t border-gray-200 pt-8">
              <p className="text-gray-600 mb-4 text-center">
                Free, secure, and powerful audio & video to MP3 converter - Convert FLAC, M4A, WAV, AAC, OGG, MP4, MOV to MP3 online
              </p>
              <p className="text-sm text-gray-500 text-center">
                © {new Date().getFullYear()} ToMP3. Made with ❤️ for creators worldwide.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
