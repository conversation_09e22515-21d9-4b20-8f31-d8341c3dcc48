import { Metadata } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "AVI to MP3 Converter - Free Online Extract Audio from AVI Videos | ToMP3",
  description: "Convert AVI to MP3 online for free. Extract audio from AVI video files with high quality. No upload required, browser-based conversion. 100% secure and private.",
  keywords: "avi to mp3, avi converter, avi to audio, video to mp3, extract audio from avi, online avi converter, convert avi files, classic video converter, free avi to mp3 converter",
  openGraph: {
    title: "AVI to MP3 Converter - Free Online Extract Audio from AVI Videos",
    description: "Convert AVI to MP3 online for free. Extract audio from AVI video files with high quality. No upload required, browser-based conversion.",
    url: `${seoConfig.baseUrl}/avi-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "AVI to MP3 Converter - Free Online Extract Audio from AVI Videos"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "AVI to MP3 Converter - Free Online Extract Audio from AVI Videos",
    description: "Convert AVI to MP3 online for free. Extract audio from AVI video files with high quality.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/avi-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function AviToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}