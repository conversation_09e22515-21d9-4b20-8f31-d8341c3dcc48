import { Metadata } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "WEBM to MP3 Converter - Free Online Extract Audio from WEBM Videos | ToMP3",
  description: "Convert WEBM to MP3 online for free. Extract audio from WEBM video files including YouTube videos. No upload required, browser-based conversion. 100% secure and private.",
  keywords: "webm to mp3, webm converter, youtube webm to mp3, video to audio, extract audio from webm, online webm converter, convert webm files, webm audio extractor, free webm to mp3 converter",
  openGraph: {
    title: "WEBM to MP3 Converter - Free Online Extract Audio from WEBM Videos",
    description: "Convert WEBM to MP3 online for free. Extract audio from WEBM video files including YouTube videos. No upload required, browser-based conversion.",
    url: `${seoConfig.baseUrl}/webm-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "WEBM to MP3 Converter - Free Online Extract Audio from WEBM Videos"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "WEBM to MP3 Converter - Free Online Extract Audio from WEBM Videos",
    description: "Convert WEBM to MP3 online for free. Extract audio from WEBM video files including YouTube videos.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/webm-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function WebmToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}