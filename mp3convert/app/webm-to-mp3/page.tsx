"use client";

import React, { useState, useRef } from "react";
import { arrayBufferToBlob, createDownloadUrl } from "@/lib/audioConverter";
import Link from "next/link";

export default function WebmToMp3() {
  // State for file handling
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [conversionProgress, setConversionProgress] = useState<number>(0);
  const [convertedFile, setConvertedFile] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Refs for file inputs
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleVideoFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  // Process file (shared between click and drag)
  const processFile = (file: File) => {
    // Check if it's a WEBM file
    if (!file.name.toLowerCase().endsWith('.webm') && !file.type.includes('webm')) {
      setError("Please select a WEBM file (.webm)");
      return;
    }
    setVideoFile(file);
    setError(null);
    setConvertedFile(null);
  };

  // Handle drag events
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  // Handle label click to prevent triggering after drag
  const handleLabelClick = (event: React.MouseEvent<HTMLLabelElement>) => {
    if (isDragging) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  // Convert file function
  const convertFile = async () => {
    if (!videoFile) {
      setError("Please select a WEBM file first");
      return;
    }

    setIsConverting(true);
    setConversionProgress(0);
    setError(null);
    
    try {
      // Create a new Web Worker for video conversion
      const worker = new Worker(new URL("../../lib/audioConverter.worker.ts", import.meta.url));
      
      // Listen for messages from the worker
      worker.onmessage = (event) => {
        const { type: messageType, result, error, progress } = event.data;

        if (messageType === "progress") {
          setConversionProgress(progress);
        } else if (messageType === "success") {
          // Create download URL
          const blob = arrayBufferToBlob(result, "audio/mpeg");
          const url = createDownloadUrl(blob);
          setConvertedFile(url);
          setIsConverting(false);
          setConversionProgress(100);
        } else if (messageType === "error") {
          setError(error || "Conversion failed");
          setIsConverting(false);
          setConversionProgress(0);
        }
      };

      // Send file to worker
      worker.postMessage({
        type: "video",
        file: videoFile
      });

    } catch {
      setError("Failed to convert file. Please try again.");
      setIsConverting(false);
    }
  };

  // Reset converter
  const resetConverter = () => {
    setVideoFile(null);
    setConvertedFile(null);
    setError(null);
    setConversionProgress(0);
    setIsConverting(false);
    if (videoInputRef.current) {
      videoInputRef.current.value = "";
    }
  };

  // Structured Data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "WEBM to MP3 Converter - Free Online Extract Audio from WEBM Videos",
    "description": "Convert WEBM to MP3 online for free. Extract audio from WEBM video files including YouTube videos. No upload required, browser-based conversion. 100% secure and private.",
    "url": "https://www.tomp3.online/webm-to-mp3",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "WEBM to MP3 conversion",
      "Extract audio from WEBM videos",
      "No file upload required",
      "High-quality audio extraction",
      "Browser-based conversion",
      "Free unlimited conversions",
      "YouTube WEBM support",
      "Fast processing",
      "Works on all devices"
    ],
    "browserRequirements": "Requires JavaScript and WebAssembly support",
    "softwareVersion": "1.0",
    "author": {
      "@type": "Organization",
      "name": "ToMP3"
    }
  };

  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How do I convert WEBM to MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Simply upload your WEBM video file to our converter, click the convert button, and download the extracted MP3 audio. The entire process happens in your browser."
        }
      },
      {
        "@type": "Question",
        "name": "Can I convert YouTube WEBM files to MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, if you have downloaded a WEBM file from YouTube or any other source, you can convert it to MP3 using our converter."
        }
      },
      {
        "@type": "Question",
        "name": "What is WEBM format?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "WEBM is an open-source video format developed by Google, commonly used for web videos and YouTube. It provides good quality with efficient compression."
        }
      },
      {
        "@type": "Question",
        "name": "Is WEBM to MP3 conversion free?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our WEBM to MP3 converter is completely free with no hidden costs, registration requirements, or file limits."
        }
      }
    ]
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-teal-50 to-cyan-50">
        {/* Navigation Bar */}
        <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-teal-600 to-cyan-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">
                  ToMP3
                </span>
              </Link>
              <div className="hidden sm:flex items-center space-x-6">
                <Link href="/mp4-to-mp3" className="text-gray-600 hover:text-teal-600 transition-colors">MP4 to MP3</Link>
                <Link href="/mov-to-mp3" className="text-gray-600 hover:text-teal-600 transition-colors">MOV to MP3</Link>
                <Link href="/wma-to-mp3" className="text-gray-600 hover:text-teal-600 transition-colors">WMA to MP3</Link>
                <Link href="/flac-to-mp3" className="text-gray-600 hover:text-teal-600 transition-colors">FLAC to MP3</Link>
                <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-teal-600 transition-colors">M4A to MP3</Link>
                <Link href="/help" className="text-gray-600 hover:text-teal-600 transition-colors">Help</Link>
                <Link href="/" className="text-gray-600 hover:text-teal-600 transition-colors">All Converters</Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              WEBM to MP3 Converter
              <span className="block bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">
                Extract Audio from WEBM Videos
              </span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Convert <strong>WEBM videos to MP3</strong> audio online for free. Extract high-quality audio from WEBM files including YouTube videos and web content. 
              <span className="font-semibold text-gray-800"> No upload required - 100% secure browser-based conversion.</span>
            </p>
            
            {/* SEO-rich description */}
            <div className="mt-6 text-base text-gray-600 max-w-3xl mx-auto">
              <p>
                Our <strong>free WEBM to MP3 converter online</strong> helps you extract audio from <strong>WEBM video files</strong>. Whether you have WEBM videos from YouTube, screen recordings, or web downloads, our <strong>browser-based WEBM converter</strong> extracts the audio track instantly without uploading files to any server. Perfect for converting <strong>YouTube WEBM to MP3</strong> or any web video content.
              </p>
            </div>
          </div>

          {/* Converter Tool */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-12">
            <div className="bg-gradient-to-r from-teal-500 to-cyan-600 p-6 text-white">
              <h2 className="text-2xl font-bold mb-2">WEBM to MP3 Converter Tool</h2>
              <p className="text-teal-100">Upload your WEBM video and extract MP3 audio instantly</p>
            </div>

            <div className="p-8">
              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-teal-400 transition-colors mb-6"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  ref={videoInputRef}
                  type="file"
                  accept=".webm,video/webm"
                  onChange={handleVideoFileSelect}
                  className="hidden"
                  id="webm-upload"
                />
                <label htmlFor="webm-upload" className="cursor-pointer" onClick={handleLabelClick}>
                  <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {videoFile ? videoFile.name : "Drop your WEBM video here"}
                  </h3>
                  <p className="text-gray-600">or click to browse</p>
                  <p className="text-sm text-gray-500 mt-2">Supports WEBM video files (.webm)</p>
                </label>
              </div>

              {/* Convert Button */}
              <button
                onClick={convertFile}
                disabled={!videoFile || isConverting}
                className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 ${
                  !videoFile || isConverting
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-teal-600 to-cyan-600 text-white hover:from-teal-700 hover:to-cyan-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                }`}
              >
                {isConverting ? "Extracting Audio..." : "Convert WEBM to MP3"}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8 rounded-r-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p className="text-red-700 font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Success Message */}
          {convertedFile && (
            <div className="bg-white border border-gray-200 rounded-2xl p-8 mb-8 shadow-lg">
              <div className="text-center">
                {/* Success Icon */}
                <div className="flex justify-center mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>

                {/* Success Title */}
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  🎉 Audio Extracted Successfully!
                </h3>
                <p className="text-gray-600 mb-2">Your WEBM video has been converted to MP3</p>
                <p className="text-sm text-gray-500 mb-8">High-quality MP3 audio is ready for download</p>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href={convertedFile}
                    download="converted_webm_to_mp3.mp3"
                    className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download MP3 File
                  </a>
                  <button
                    onClick={resetConverter}
                    className="inline-flex items-center justify-center px-8 py-4 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200 border border-gray-200 hover:border-gray-300"
                  >
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Convert Another Video
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Progress Section */}
          {isConverting && (
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-teal-100 rounded-full mb-4">
                  <svg className="w-8 h-8 text-teal-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Extracting Audio from WEBM...</h3>
                <p className="text-gray-600 mb-6">Please wait while we extract the audio track</p>
                
                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                  <div 
                    className="bg-gradient-to-r from-teal-600 to-cyan-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${conversionProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500">{conversionProgress}% complete</p>
              </div>
            </div>
          )}

          {/* About WEBM Format */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">About WEBM to MP3 Conversion</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">What is WEBM?</h3>
                <p className="text-gray-600 mb-4">
                  WEBM is an open-source video format developed by Google, widely used for web videos, YouTube content, and HTML5 video streaming.
                </p>
                <ul className="text-gray-600 space-y-2">
                  <li>• Open-source video format</li>
                  <li>• YouTube&apos;s preferred format</li>
                  <li>• Excellent compression</li>
                  <li>• HTML5 compatible</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Why Extract Audio to MP3?</h3>
                <p className="text-gray-600 mb-4">
                  Extracting audio from WEBM videos allows you to enjoy music, podcasts, or audio content from videos on any device.
                </p>
                <ul className="text-gray-600 space-y-2">
                  <li>• Save storage space</li>
                  <li>• Create audio playlists</li>
                  <li>• Listen offline anywhere</li>
                  <li>• Universal compatibility</li>
                </ul>
              </div>
            </div>

            {/* Additional SEO Content */}
            <div className="mt-8 pt-8 border-t border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">How to Convert WEBM to MP3 Online</h3>
              <ol className="text-gray-600 space-y-3">
                <li><strong>Step 1:</strong> Click the upload area or drag and drop your WEBM video file</li>
                <li><strong>Step 2:</strong> Wait for the file to load (no server upload needed)</li>
                <li><strong>Step 3:</strong> Click &quot;Convert WEBM to MP3&quot; button</li>
                <li><strong>Step 4:</strong> Download your extracted MP3 audio file</li>
              </ol>
              
              <div className="mt-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Common WEBM to MP3 Use Cases</h4>
                <ul className="text-gray-600 space-y-2">
                  <li>• Extract audio from YouTube WEBM downloads</li>
                  <li>• Convert online course videos to audio</li>
                  <li>• Create podcasts from video content</li>
                  <li>• Extract music from web videos</li>
                  <li>• Convert screen recordings to audio</li>
                  <li>• Save conference calls as audio files</li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-gradient-to-r from-gray-50 to-teal-50 rounded-2xl p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Can I convert YouTube WEBM files?</h3>
                <p className="text-gray-600">Yes, if you have downloaded a WEBM file from YouTube or any other source, you can extract its audio to MP3 using our converter.</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">What&apos;s the quality of extracted MP3?</h3>
                <p className="text-gray-600">Our converter maintains high audio quality during extraction. The output MP3 preserves the original audio quality from the WEBM video.</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">How large can WEBM files be?</h3>
                <p className="text-gray-600">Our browser-based converter can handle WEBM files of various sizes. Processing time depends on file size and your device&apos;s performance.</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Is WEBM better than MP4?</h3>
                <p className="text-gray-600">WEBM offers better compression and is open-source, making it ideal for web use. MP4 has wider device compatibility. Both can be converted to MP3 with our tool.</p>
              </div>
            </div>
          </div>

          {/* Related Converters */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Other Video to MP3 Converters</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/mp4-to-mp3" className="bg-gradient-to-br from-orange-50 to-red-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-orange-100">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">MP4 to MP3</h3>
                <p className="text-gray-600 text-sm">Extract audio from MP4 videos</p>
              </Link>
              
              <Link href="/mov-to-mp3" className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-purple-100">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">MOV to MP3</h3>
                <p className="text-gray-600 text-sm">Convert QuickTime MOV to MP3</p>
              </Link>
              
              <Link href="/wma-to-mp3" className="bg-gradient-to-br from-red-50 to-orange-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-red-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-red-200 transition-colors">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">WMA to MP3</h3>
                <p className="text-gray-600 text-sm">Convert Windows Media Audio</p>
              </Link>
              
              <Link href="/avi-to-mp3" className="bg-gradient-to-br from-amber-50 to-yellow-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-amber-100">
                <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-amber-200 transition-colors">
                  <svg className="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">AVI to MP3</h3>
                <p className="text-gray-600 text-sm">Convert AVI videos to MP3</p>
              </Link>
              
              <Link href="/mkv-to-mp3" className="bg-gradient-to-br from-indigo-50 to-purple-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-indigo-100">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-indigo-200 transition-colors">
                  <svg className="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">MKV to MP3</h3>
                <p className="text-gray-600 text-sm">Convert MKV videos to MP3</p>
              </Link>
              
              <Link href="/3gp-to-mp3" className="bg-gradient-to-br from-emerald-50 to-green-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-emerald-100">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-emerald-200 transition-colors">
                  <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">3GP to MP3</h3>
                <p className="text-gray-600 text-sm">Convert 3GP videos to MP3</p>
              </Link>
              
              <Link href="/opus-to-mp3" className="bg-gradient-to-br from-violet-50 to-purple-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-violet-100">
                <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-violet-200 transition-colors">
                  <svg className="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">OPUS to MP3</h3>
                <p className="text-gray-600 text-sm">Convert OPUS audio files</p>
              </Link>
              
              <Link href="/ape-to-mp3" className="bg-gradient-to-br from-rose-50 to-pink-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-rose-100">
                <div className="w-12 h-12 bg-rose-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-rose-200 transition-colors">
                  <svg className="w-6 h-6 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">APE to MP3</h3>
                <p className="text-gray-600 text-sm">Convert Monkey&apos;s Audio files</p>
              </Link>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}