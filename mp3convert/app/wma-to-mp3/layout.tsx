import { Metadata } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "WMA to MP3 Converter - Free Online Windows Media Audio to MP3 | ToMP3",
  description: "Convert WMA to MP3 online for free. Transform Windows Media Audio files to universal MP3 format with no upload required. High-quality browser-based conversion. 100% secure and private.",
  keywords: "wma to mp3, wma converter, windows media audio to mp3, audio converter, free wma converter, online wma to mp3, convert wma files, wma to mp3 converter online, windows media audio converter",
  openGraph: {
    title: "WMA to MP3 Converter - Free Online Windows Media Audio to MP3",
    description: "Convert WMA to MP3 online for free. Transform Windows Media Audio files to universal MP3 format with no upload required. High-quality browser-based conversion.",
    url: `${seoConfig.baseUrl}/wma-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "WMA to MP3 Converter - Free Online Windows Media Audio to MP3"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "WMA to MP3 Converter - Free Online Windows Media Audio to MP3",
    description: "Convert WMA to MP3 online for free. Transform Windows Media Audio files to universal MP3 format. High-quality browser-based conversion.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/wma-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function WmaToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}