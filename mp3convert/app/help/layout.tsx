import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Help & Support | How to Convert Audio & Video to MP3 | ToMP3",
  description: "Learn how to use our free audio and video to MP3 converter. Find answers to common questions about FLAC to MP3, M4A to MP3, MOV to MP3, and MP4 to MP3 conversion.",
  keywords: "help, support, how to convert, mp3 converter help, audio converter guide, video to mp3 help, flac to mp3 help, m4a to mp3 help, mov to mp3 help, mp4 to mp3 help",
  openGraph: {
    title: "Help & Support | ToMP3",
    description: "Learn how to use our free audio and video to MP3 converter. Get help with file conversion and find answers to common questions.",
    url: "https://www.tomp3.online/help",
    type: "website",
  },
  twitter: {
    title: "Help & Support | ToMP3",
    description: "Learn how to use our free audio and video to MP3 converter.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/help",
  },
};

export default function HelpLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
