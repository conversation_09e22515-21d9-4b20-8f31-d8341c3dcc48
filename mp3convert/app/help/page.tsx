import Link from "next/link";

export default function Help() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                ToMP3
              </span>
            </Link>
            <div className="hidden sm:flex items-center space-x-6">
              <Link href="/flac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">FLAC to MP3</Link>
              <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">M4A to MP3</Link>
              <Link href="/mov-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">MOV to MP3</Link>
              <Link href="/mp4-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">MP4 to MP3</Link>
              <span className="text-blue-600 font-medium">Help</span>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Help & Support
            <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Everything You Need to Know
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Learn how to use our free audio and video to MP3 converter. Find answers to common questions and get the most out of our conversion tools.
          </p>
        </div>

        {/* Quick Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Link href="/flac-to-mp3" className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-center group">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">FLAC to MP3</h3>
            <p className="text-gray-600 text-sm">Convert lossless FLAC files</p>
          </Link>

          <Link href="/m4a-to-mp3" className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-center group">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">M4A to MP3</h3>
            <p className="text-gray-600 text-sm">Convert iTunes M4A files</p>
          </Link>

          <Link href="/mov-to-mp3" className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-center group">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">MOV to MP3</h3>
            <p className="text-gray-600 text-sm">Extract audio from MOV videos</p>
          </Link>

          <Link href="/mp4-to-mp3" className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-center group">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors">
              <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">MP4 to MP3</h3>
            <p className="text-gray-600 text-sm">Extract audio from MP4 videos</p>
          </Link>
        </div>

        {/* How to Use Section */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">How to Convert Files to MP3</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Choose Format</h3>
              <p className="text-gray-600">Select the converter for your file type: FLAC to MP3, M4A to MP3, MOV to MP3, or MP4 to MP3.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Upload File</h3>
              <p className="text-gray-600">Drag and drop your file or click to browse. Your file stays on your device - no upload to servers.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Download MP3</h3>
              <p className="text-gray-600">Click convert and download your high-quality MP3 file. It&apos;s that simple!</p>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>
          
          <div className="space-y-8">
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Is this converter really free?</h3>
              <p className="text-gray-600">Yes! Our audio and video to MP3 converter is completely free with no hidden costs, registration requirements, or file limits. Convert as many files as you want.</p>
            </div>

            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Are my files secure and private?</h3>
              <p className="text-gray-600">Absolutely! All conversion happens directly in your browser using WebAssembly technology. Your files never leave your device, ensuring complete privacy and security.</p>
            </div>

            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">What file formats do you support?</h3>
              <p className="text-gray-600">We support popular audio formats (FLAC, M4A, WAV, AAC, OGG, WMA) and video formats (MP4, MOV, AVI, MKV, WMV, FLV) for conversion to MP3.</p>
            </div>

            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">What&apos;s the maximum file size?</h3>
              <p className="text-gray-600">For optimal performance, we recommend files under 100MB for audio and 500MB for video. Larger files may take longer to process but will still work.</p>
            </div>

            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Which browsers are supported?</h3>
              <p className="text-gray-600">Our converter works on all modern browsers including Chrome, Firefox, Safari, and Edge. WebAssembly support is required for the conversion process.</p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">How is the audio quality?</h3>
              <p className="text-gray-600">We maintain high audio quality during conversion. The output MP3 files use optimized settings to preserve as much of the original audio quality as possible.</p>
            </div>
          </div>
        </div>

        {/* Supported Formats */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Supported File Formats</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Audio Formats</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-blue-700">FLAC</span>
                </div>
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-green-700">M4A</span>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-purple-700">WAV</span>
                </div>
                <div className="bg-orange-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-orange-700">AAC</span>
                </div>
                <div className="bg-pink-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-pink-700">OGG</span>
                </div>
                <div className="bg-indigo-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-indigo-700">WMA</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Video Formats</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-red-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-red-700">MP4</span>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-purple-700">MOV</span>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-blue-700">AVI</span>
                </div>
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-green-700">MKV</span>
                </div>
                <div className="bg-yellow-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-yellow-700">WMV</span>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg text-center">
                  <span className="font-semibold text-gray-700">FLV</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
