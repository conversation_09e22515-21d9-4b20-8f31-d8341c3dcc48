import { Metadata } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "MKV to MP3 Converter - Free Online Extract Audio from MKV Videos | ToMP3",
  description: "Convert MKV to MP3 online for free. Extract audio from MKV (Matroska) video files including HD and 4K videos. No upload required, browser-based conversion. 100% secure and private.",
  keywords: "mkv to mp3, mkv converter, matroska to mp3, video to audio, extract audio from mkv, hd video to mp3, 4k video to mp3, online mkv converter, free mkv to mp3 converter",
  openGraph: {
    title: "MKV to MP3 Converter - Free Online Extract Audio from MKV Videos",
    description: "Convert MKV to MP3 online for free. Extract audio from MKV (Matroska) video files including HD and 4K videos. No upload required, browser-based conversion.",
    url: `${seoConfig.baseUrl}/mkv-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "MKV to MP3 Converter - Free Online Extract Audio from MKV Videos"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "MKV to MP3 Converter - Free Online Extract Audio from MKV Videos",
    description: "Convert MKV to MP3 online for free. Extract audio from MKV (Matroska) video files including HD and 4K videos.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/mkv-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function MkvToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}