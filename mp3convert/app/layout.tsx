import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Free Audio & Video to MP3 Converter Online | FLAC, M4A, WAV, AAC, MOV, MP4 to MP3 | ToMP3",
  description: "Convert FLAC to MP3, M4A to MP3, WAV to MP3, AAC to MP3, OGG to MP3, MOV to MP3, and MP4 to MP3 online for free. High-quality conversion with no upload required. 100% secure browser-based conversion using WebAssembly technology.",
  keywords: "flac to mp3, m4a to mp3, wav to mp3, aac to mp3, ogg to mp3, mov to mp3, mp4 to mp3, free audio converter online, browser based mp3 converter, no upload mp3 converter, secure audio conversion, webassembly audio converter, lossless to mp3, video to audio converter, flac to mp3 converter online free, m4a to mp3 converter online free, wav to mp3 converter online free, mp4 to mp3 converter online free, audio file converter, video file converter, online mp3 converter, free mp3 converter, high quality mp3 conversion, private audio converter, no registration converter, unlimited mp3 conversion",
  authors: [{ name: "ToMP3" }],
  creator: "ToMP3",
  publisher: "ToMP3",
  applicationName: "ToMP3 - Free Audio & Video to MP3 Converter",
  category: "Multimedia",
  classification: "Audio Video Converter Tool",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  // 注意：使用Cloudflare DNS验证，无需在代码中添加Google验证码
  alternates: {
    canonical: "https://www.tomp3.online",
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://www.tomp3.online",
    title: "Free Audio & Video to MP3 Converter Online | ToMP3",
    description: "Convert FLAC to MP3, M4A to MP3, MOV to MP3, and MP4 to MP3 online for free. High-quality conversion with no upload required.",
    siteName: "ToMP3",
    images: [
      {
        url: "https://www.tomp3.online/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "ToMP3 - Free Online Audio & Video to MP3 Converter",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@tomp3online",
    creator: "@tomp3online",
    title: "Free Audio & Video to MP3 Converter Online | ToMP3",
    description: "Convert FLAC to MP3, M4A to MP3, MOV to MP3, and MP4 to MP3 online for free. No upload required - secure browser-based conversion.",
    images: ["https://www.tomp3.online/og-image.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Additional meta tags for better social media support */}
        <meta property="og:image:type" content="image/jpeg" />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta name="twitter:image:alt" content="ToMP3 - Free Online Audio & Video to MP3 Converter" />
        <meta name="theme-color" content="#1e3a8a" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
