"use client";

import React from "react";
import Link from "next/link";

export default function AudioFormatsGuide() {
  // Structured Data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "Complete Guide to Audio Formats: MP3, FLAC, WAV, AAC, WMA, OPUS, APE",
    "description": "Comprehensive guide to audio formats including MP3, FLAC, WAV, AAC, WMA, OPUS, and APE. Learn about compression, quality, and when to use each format.",
    "author": {
      "@type": "Organization",
      "name": "ToMP3"
    },
    "publisher": {
      "@type": "Organization",
      "name": "ToMP3",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.tomp3.online/icon.svg"
      }
    },
    "datePublished": new Date().toISOString(),
    "dateModified": new Date().toISOString()
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        {/* Navigation Bar */}
        <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  ToMP3
                </span>
              </Link>
              <div className="hidden sm:flex items-center space-x-6">
                <Link href="/flac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">FLAC to MP3</Link>
                <Link href="/wav-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">WAV to MP3</Link>
                <Link href="/wma-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">WMA to MP3</Link>
                <Link href="/opus-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">OPUS to MP3</Link>
                <Link href="/ape-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors">APE to MP3</Link>
                <Link href="/help" className="text-gray-600 hover:text-blue-600 transition-colors">Help</Link>
                <Link href="/" className="text-gray-600 hover:text-blue-600 transition-colors">All Converters</Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Complete Audio Formats Guide
              <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Understanding MP3, FLAC, WAV, and More
              </span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Comprehensive guide to audio formats including <strong>MP3, FLAC, WAV, AAC, WMA, OPUS, and APE</strong>. Learn about compression, quality, and when to use each format for optimal results.
            </p>
          </div>

          {/* Introduction */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Understanding Audio Formats</h2>
            <p className="text-gray-600 mb-4">
              Audio formats are file containers that store digital audio data. They differ in compression methods, quality, and file sizes. Understanding these differences helps you choose the right format for your needs.
            </p>
            <p className="text-gray-600">
              There are two main categories of audio formats: <strong>lossless</strong> (preserves all original audio data) and <strong>lossy</strong> (removes some data to reduce file size). Each has its own advantages depending on your use case.
            </p>
          </div>

          {/* Audio Formats Comparison */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Audio Formats Comparison</h2>
            
            {/* MP3 */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">MP3 (MPEG Audio Layer III)</h3>
                  <span className="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full mt-2">Lossy Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                MP3 is the most widely supported audio format. It uses lossy compression to significantly reduce file sizes while maintaining acceptable quality.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Universal compatibility</li>
                    <li>• Small file sizes</li>
                    <li>• Good quality at moderate bitrates</li>
                    <li>• Supported by all devices</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Lossy compression</li>
                    <li>• Quality loss at low bitrates</li>
                    <li>• Limited metadata support</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* FLAC */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">FLAC (Free Lossless Audio Codec)</h3>
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-2">Lossless Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                FLAC is a popular lossless compression format that reduces file sizes without losing any audio quality. It&#39;s widely used for archiving and high-quality audio.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Lossless compression</li>
                    <li>• Open-source format</li>
                    <li>• Good compression ratio</li>
                    <li>• Metadata support</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Larger file sizes than MP3</li>
                    <li>• Limited device support</li>
                    <li>• Not ideal for streaming</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* WAV */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">WAV (Waveform Audio File Format)</h3>
                  <span className="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mt-2">Uncompressed Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                WAV is an uncompressed audio format that provides the highest quality audio but results in very large file sizes. It&#39;s the standard for professional audio.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Uncompressed audio</li>
                    <li>• Highest quality</li>
                    <li>• Professional standard</li>
                    <li>• Universal compatibility</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Very large file sizes</li>
                    <li>• Not efficient for storage</li>
                    <li>• Limited metadata support</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* AAC */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">AAC (Advanced Audio Coding)</h3>
                  <span className="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full mt-2">Lossy Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                AAC is a lossy audio format that&#39;s the successor to MP3. It provides better quality at similar bitrates and is the standard for iTunes and YouTube.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Better quality than MP3</li>
                    <li>• Efficient compression</li>
                    <li>• iTunes standard</li>
                    <li>• YouTube audio format</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Limited device support</li>
                    <li>• Lossy compression</li>
                    <li>• Not as universal as MP3</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* WMA */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">WMA (Windows Media Audio)</h3>
                  <span className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full mt-2">Lossy Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                WMA is Microsoft&#39;s proprietary audio format, commonly used in Windows Media Player and older Windows devices.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Good compression ratio</li>
                    <li>• Windows integration</li>
                    <li>• DRM support</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Limited device compatibility</li>
                    <li>• Proprietary format</li>
                    <li>• Not widely supported</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* OPUS */}
            <div className="border-b border-gray-200 pb-8 mb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">OPUS</h3>
                  <span className="inline-block bg-violet-100 text-violet-800 text-xs px-2 py-1 rounded-full mt-2">Lossy Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                OPUS is a modern, open-source audio codec designed for interactive speech and music transmission over the Internet. It&#39;s known for high compression efficiency and excellent quality.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Modern codec</li>
                    <li>• Open-source</li>
                    <li>• High efficiency</li>
                    <li>• Used by Discord, WhatsApp</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Limited device support</li>
                    <li>• Not as universal as MP3</li>
                    <li>• Newer format</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* APE */}
            <div className="pb-8">
              <div className="flex items-start mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-pink-500 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">APE (Monkey&#39;s Audio)</h3>
                  <span className="inline-block bg-rose-100 text-rose-800 text-xs px-2 py-1 rounded-full mt-2">Lossless Format</span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">
                APE is a lossless audio compression format that provides perfect reproduction of original audio. It&#39;s popular among audio enthusiasts for archiving high-quality music.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Advantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Lossless compression</li>
                    <li>• Perfect audio reproduction</li>
                    <li>• Smaller than WAV files</li>
                    <li>• Popular among audiophiles</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Disadvantages:</h4>
                  <ul className="text-gray-600 space-y-1">
                    <li>• Limited device support</li>
                    <li>• Proprietary format</li>
                    <li>• Not widely compatible</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* When to Use Each Format */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">When to Use Each Audio Format</h2>
            
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use MP3 for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• General music listening on any device</li>
                  <li>• Sharing music with others</li>
                  <li>• Podcasts and audiobooks</li>
                  <li>• Streaming and online use</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-indigo-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use FLAC for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Archiving your music collection</li>
                  <li>• High-quality listening at home</li>
                  <li>• Professional audio work</li>
                  <li>• When you want lossless quality</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-purple-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use WAV for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Professional audio editing</li>
                  <li>• Studio recordings</li>
                  <li>• When maximum quality is required</li>
                  <li>• As an intermediate format</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-orange-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use AAC for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• iTunes and Apple devices</li>
                  <li>• YouTube audio</li>
                  <li>• When you want better quality than MP3</li>
                  <li>• Modern streaming services</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-red-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use WMA for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Windows Media Player</li>
                  <li>• Legacy Windows systems</li>
                  <li>• When you have existing WMA files</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-violet-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use OPUS for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Discord voice calls</li>
                  <li>• WhatsApp audio messages</li>
                  <li>• Modern communication platforms</li>
                  <li>• When you want efficient compression</li>
                </ul>
              </div>
              
              <div className="border-l-4 border-rose-500 pl-4 py-1">
                <h3 className="font-semibold text-gray-900 mb-2">Use APE for:</h3>
                <ul className="text-gray-600 space-y-1">
                  <li>• Archiving high-quality music</li>
                  <li>• Audio enthusiast collections</li>
                  <li>• When you want lossless compression</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Conversion Tools */}
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Convert Between Audio Formats</h2>
            <p className="text-gray-600 text-center mb-8">
              Use our free online converters to transform your audio files between different formats
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/flac-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group border border-gray-200">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">FLAC to MP3</h3>
                <p className="text-gray-600 text-sm">Convert lossless FLAC to MP3</p>
              </Link>
              
              <Link href="/wav-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group border border-gray-200">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">WAV to MP3</h3>
                <p className="text-gray-600 text-sm">Compress WAV files to MP3</p>
              </Link>
              
              <Link href="/wma-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group border border-gray-200">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">WMA to MP3</h3>
                <p className="text-gray-600 text-sm">Convert Windows Media Audio</p>
              </Link>
              
              <Link href="/opus-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group border border-gray-200">
                <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">OPUS to MP3</h3>
                <p className="text-gray-600 text-sm">Convert modern OPUS audio</p>
              </Link>
              
              <Link href="/ape-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group border border-gray-200">
                <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">APE to MP3</h3>
                <p className="text-gray-600 text-sm">Convert Monkey&apos;s Audio files</p>
              </Link>
              
              <Link href="/aac-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group border border-gray-200">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">AAC to MP3</h3>
                <p className="text-gray-600 text-sm">Convert iTunes audio files</p>
              </Link>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}