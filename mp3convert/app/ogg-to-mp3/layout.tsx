import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "OGG to MP3 Converter Free Online | Open Source Audio to MP3 | ToMP3",
  description: "Convert OGG to MP3 online for free. Transform open-source OGG Vorbis files to universal MP3 format for maximum compatibility. No upload required - 100% secure and private.",
  keywords: "ogg to mp3, ogg to mp3 converter, convert ogg to mp3, ogg to mp3 online, free ogg to mp3, ogg vorbis, open source audio converter",
  openGraph: {
    title: "OGG to MP3 Converter Free Online | ToMP3",
    description: "Convert OGG to MP3 online for free. Transform open-source OGG Vorbis files to universal MP3 format for maximum compatibility.",
    url: "https://www.tomp3.online/ogg-to-mp3",
    type: "website",
  },
  twitter: {
    title: "OGG to MP3 Converter Free Online | ToMP3",
    description: "Convert OGG to MP3 online for free. Open source audio to MP3 conversion.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/ogg-to-mp3",
  },
};

export default function OggToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
