"use client";

import { useState, useRef } from "react";
import { arrayBufferToBlob, createDownloadUrl } from "@/lib/audioConverter";
import Link from "next/link";

export default function OggToMp3() {
  // State for file handling
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [conversionProgress, setConversionProgress] = useState<number>(0);
  const [convertedFile, setConvertedFile] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Refs for file inputs
  const audioInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleAudioFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check if it's an OGG file
      if (!file.name.toLowerCase().endsWith('.ogg') && !file.type.includes('ogg')) {
        setError("Please select an OGG file (.ogg)");
        return;
      }
      setAudioFile(file);
      setError(null);
      setConvertedFile(null);
    }
  };

  // Convert file function
  const convertFile = async () => {
    if (!audioFile) {
      setError("Please select an OGG file first");
      return;
    }

    setIsConverting(true);
    setConversionProgress(0);
    setError(null);
    
    try {
      // Create a new Web Worker for audio conversion
      const worker = new Worker(new URL("../../lib/audioConverter.worker.ts", import.meta.url));
      
      // Listen for messages from the worker
      worker.onmessage = (event) => {
        const { type: messageType, result, error, progress } = event.data;

        if (messageType === "progress") {
          setConversionProgress(progress);
        } else if (messageType === "success") {
          // Create download URL
          const blob = arrayBufferToBlob(result, "audio/mpeg");
          const url = createDownloadUrl(blob);
          setConvertedFile(url);
          setIsConverting(false);
          setConversionProgress(100);
        } else if (messageType === "error") {
          setError(error || "Conversion failed");
          setIsConverting(false);
          setConversionProgress(0);
        }
      };

      // Send file to worker
      worker.postMessage({
        type: "audio",
        file: audioFile
      });

    } catch {
      setError("Failed to convert file. Please try again.");
      setIsConverting(false);
    }
  };

  // Reset converter
  const resetConverter = () => {
    setAudioFile(null);
    setConvertedFile(null);
    setError(null);
    setConversionProgress(0);
    setIsConverting(false);
    if (audioInputRef.current) {
      audioInputRef.current.value = "";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-pink-50 to-rose-50">
      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-pink-600 to-rose-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                ToMP3
              </span>
            </Link>
            <div className="hidden lg:flex items-center space-x-4">
              <Link href="/flac-to-mp3" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">FLAC</Link>
              <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">M4A</Link>
              <Link href="/wav-to-mp3" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">WAV</Link>
              <Link href="/aac-to-mp3" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">AAC</Link>
              <span className="text-pink-600 font-medium text-sm">OGG</span>
              <Link href="/mov-to-mp3" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">MOV</Link>
              <Link href="/mp4-to-mp3" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">MP4</Link>
              <Link href="/help" className="text-gray-600 hover:text-pink-600 transition-colors text-sm">Help</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            OGG to MP3 Converter
            <span className="block bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
              Open Source Audio Format
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Convert your <strong>OGG files to MP3</strong> format online for free. Transform open-source OGG Vorbis files to universal MP3 format. 
            <span className="font-semibold text-gray-800">No upload required - 100% secure and private.</span>
          </p>
        </div>

        {/* Converter Tool */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-12">
          <div className="bg-gradient-to-r from-pink-500 to-rose-600 p-6 text-white">
            <h2 className="text-2xl font-bold mb-2">OGG to MP3 Converter Tool</h2>
            <p className="text-pink-100">Upload your OGG file and convert to MP3 instantly</p>
          </div>

          <div className="p-8">
            {/* File Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-pink-400 transition-colors mb-6">
              <input
                ref={audioInputRef}
                type="file"
                accept=".ogg,audio/ogg"
                onChange={handleAudioFileSelect}
                className="hidden"
                id="ogg-upload"
              />
              <label htmlFor="ogg-upload" className="cursor-pointer">
                <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {audioFile ? audioFile.name : "Drop your OGG file here"}
                </h3>
                <p className="text-gray-600">or click to browse</p>
                <p className="text-sm text-gray-500 mt-2">Supports OGG Vorbis audio files</p>
              </label>
            </div>

            {/* Convert Button */}
            <button
              onClick={convertFile}
              disabled={!audioFile || isConverting}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 ${
                !audioFile || isConverting
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-pink-600 to-rose-600 text-white hover:from-pink-700 hover:to-rose-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              }`}
            >
              {isConverting ? "Converting..." : "Convert OGG to MP3"}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8 rounded-r-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Success Message */}
        {convertedFile && (
          <div className="bg-white border border-gray-200 rounded-2xl p-8 mb-8 shadow-lg">
            <div className="text-center">
              {/* Success Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>

              {/* Success Title */}
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                🎉 Conversion Successful!
              </h3>
              <p className="text-gray-600 mb-2">Your OGG file has been converted to MP3</p>
              <p className="text-sm text-gray-500 mb-8">High-quality MP3 file is ready for download</p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href={convertedFile}
                  download="converted_ogg_to_mp3.mp3"
                  className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Download MP3 File
                </a>
                <button
                  onClick={resetConverter}
                  className="inline-flex items-center justify-center px-8 py-4 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200 border border-gray-200 hover:border-gray-300"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Convert Another File
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Progress Section */}
        {isConverting && (
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-pink-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-pink-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Converting OGG to MP3...</h3>
              <p className="text-gray-600 mb-6">Please wait while we process your file</p>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div 
                  className="bg-gradient-to-r from-pink-600 to-rose-600 h-3 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${conversionProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">{conversionProgress}% complete</p>
            </div>
          </div>
        )}

        {/* About OGG Format */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">About OGG to MP3 Conversion</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">What is OGG?</h3>
              <p className="text-gray-600 mb-4">
                OGG is an open-source audio format that provides good compression and quality. It&apos;s patent-free and widely used in open-source applications.
              </p>
              <ul className="text-gray-600 space-y-2">
                <li>• Open-source audio format</li>
                <li>• Patent-free technology</li>
                <li>• Good compression efficiency</li>
                <li>• Used in gaming and web</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Why Convert to MP3?</h3>
              <p className="text-gray-600 mb-4">
                Converting OGG to MP3 ensures compatibility with all devices and platforms, especially mobile devices and older audio players.
              </p>
              <ul className="text-gray-600 space-y-2">
                <li>• Universal device support</li>
                <li>• Better mobile compatibility</li>
                <li>• Wider software support</li>
                <li>• Industry standard format</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Related Converters */}
        <div className="bg-gradient-to-r from-gray-50 to-pink-50 rounded-2xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Other Audio Converters</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link href="/flac-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">FLAC to MP3</h3>
              <p className="text-gray-600 text-sm">Convert lossless FLAC files to MP3</p>
            </Link>
            
            <Link href="/wav-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">WAV to MP3</h3>
              <p className="text-gray-600 text-sm">Convert uncompressed WAV to MP3</p>
            </Link>
            
            <Link href="/aac-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">AAC to MP3</h3>
              <p className="text-gray-600 text-sm">Convert Advanced Audio Codec to MP3</p>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
