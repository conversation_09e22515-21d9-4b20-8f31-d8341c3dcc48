"use client";

import React, { useState, useRef } from "react";
import { arrayBufferToBlob, createDownloadUrl } from "@/lib/audioConverter";
import Link from "next/link";

export default function OpusToMp3() {
  // State for file handling
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [conversionProgress, setConversionProgress] = useState<number>(0);
  const [convertedFile, setConvertedFile] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Refs for file inputs
  const audioInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleAudioFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check if it's an OPUS file
      if (!file.name.toLowerCase().endsWith('.opus') && !file.type.includes('opus')) {
        setError("Please select an OPUS file (.opus)");
        return;
      }
      setAudioFile(file);
      setError(null);
      setConvertedFile(null);
    }
  };

  // Convert file function
  const convertFile = async () => {
    if (!audioFile) {
      setError("Please select an OPUS file first");
      return;
    }

    setIsConverting(true);
    setConversionProgress(0);
    setError(null);
    
    try {
      // Create a new Web Worker for audio conversion
      const worker = new Worker(new URL("../../lib/audioConverter.worker.ts", import.meta.url));
      
      // Listen for messages from the worker
      worker.onmessage = (event) => {
        const { type: messageType, result, error, progress } = event.data;

        if (messageType === "progress") {
          setConversionProgress(progress);
        } else if (messageType === "success") {
          // Create download URL
          const blob = arrayBufferToBlob(result, "audio/mpeg");
          const url = createDownloadUrl(blob);
          setConvertedFile(url);
          setIsConverting(false);
          setConversionProgress(100);
        } else if (messageType === "error") {
          setError(error || "Conversion failed");
          setIsConverting(false);
          setConversionProgress(0);
        }
      };

      // Send file to worker
      worker.postMessage({
        type: "audio",
        file: audioFile
      });

    } catch {
      setError("Failed to convert file. Please try again.");
      setIsConverting(false);
    }
  };

  // Reset converter
  const resetConverter = () => {
    setAudioFile(null);
    setConvertedFile(null);
    setError(null);
    setConversionProgress(0);
    setIsConverting(false);
    if (audioInputRef.current) {
      audioInputRef.current.value = "";
    }
  };

  // Structured Data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "OPUS to MP3 Converter - Free Online Convert OPUS Audio to MP3",
    "description": "Convert OPUS to MP3 online for free. Transform OPUS audio files to universal MP3 format with no upload required. High-quality browser-based conversion. 100% secure and private.",
    "url": "https://www.tomp3.online/opus-to-mp3",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "OPUS to MP3 conversion",
      "No file upload required",
      "High-quality audio output",
      "Browser-based conversion",
      "Free unlimited conversions",
      "Modern audio codec support",
      "Fast processing",
      "Works on all devices"
    ],
    "browserRequirements": "Requires JavaScript and WebAssembly support",
    "softwareVersion": "1.0",
    "author": {
      "@type": "Organization",
      "name": "ToMP3"
    }
  };

  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How do I convert OPUS to MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Simply upload your OPUS audio file to our converter, click the convert button, and download the converted MP3 file. The entire process happens in your browser for maximum privacy."
        }
      },
      {
        "@type": "Question",
        "name": "What is OPUS audio format?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "OPUS is a modern, open-source audio codec designed for interactive speech and music transmission over the Internet. It's known for high compression efficiency and excellent quality."
        }
      },
      {
        "@type": "Question",
        "name": "Is OPUS better than MP3?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "OPUS generally provides better quality at lower bitrates than MP3. However, MP3 has universal compatibility, which is why converting OPUS to MP3 is useful for broader device support."
        }
      },
      {
        "@type": "Question",
        "name": "Is OPUS to MP3 conversion free?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our OPUS to MP3 converter is completely free with no hidden costs, registration requirements, or file limits."
        }
      }
    ]
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-violet-50 to-purple-50">
        {/* Navigation Bar */}
        <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-violet-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                  ToMP3
                </span>
              </Link>
              <div className="hidden sm:flex items-center space-x-6">
                <Link href="/flac-to-mp3" className="text-gray-600 hover:text-violet-600 transition-colors">FLAC to MP3</Link>
                <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-violet-600 transition-colors">M4A to MP3</Link>
                <Link href="/wma-to-mp3" className="text-gray-600 hover:text-violet-600 transition-colors">WMA to MP3</Link>
                <Link href="/3gp-to-mp3" className="text-gray-600 hover:text-violet-600 transition-colors">3GP to MP3</Link>
                <Link href="/help" className="text-gray-600 hover:text-violet-600 transition-colors">Help</Link>
                <Link href="/" className="text-gray-600 hover:text-violet-600 transition-colors">All Converters</Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              OPUS to MP3 Converter
              <span className="block bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                Modern Audio to Universal Format
              </span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Convert <strong>OPUS audio files to MP3</strong> format online for free. Transform modern OPUS files to universal MP3 format for compatibility with all devices. 
              <span className="font-semibold text-gray-800"> No upload required - 100% secure and private.</span>
            </p>
            
            {/* SEO-rich description */}
            <div className="mt-6 text-base text-gray-600 max-w-3xl mx-auto">
              <p>
                Our <strong>free OPUS to MP3 converter online</strong> helps you convert <strong>OPUS audio files</strong> to the universally compatible MP3 format. Whether you have OPUS files from Discord, WhatsApp, or other modern applications, our <strong>browser-based OPUS converter</strong> transforms them instantly without uploading to any server.
              </p>
            </div>
          </div>

          {/* Converter Tool */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-12">
            <div className="bg-gradient-to-r from-violet-500 to-purple-600 p-6 text-white">
              <h2 className="text-2xl font-bold mb-2">OPUS to MP3 Converter Tool</h2>
              <p className="text-violet-100">Upload your OPUS file and convert to MP3 instantly</p>
            </div>

            <div className="p-8">
              {/* File Upload Area */}
              <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-violet-400 transition-colors mb-6">
                <input
                  ref={audioInputRef}
                  type="file"
                  accept=".opus,audio/opus"
                  onChange={handleAudioFileSelect}
                  className="hidden"
                  id="opus-upload"
                />
                <label htmlFor="opus-upload" className="cursor-pointer">
                  <div className="w-16 h-16 bg-violet-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {audioFile ? audioFile.name : "Drop your OPUS file here"}
                  </h3>
                  <p className="text-gray-600">or click to browse</p>
                  <p className="text-sm text-gray-500 mt-2">Supports OPUS audio files (.opus)</p>
                </label>
              </div>

              {/* Convert Button */}
              <button
                onClick={convertFile}
                disabled={!audioFile || isConverting}
                className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 ${
                  !audioFile || isConverting
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-violet-600 to-purple-600 text-white hover:from-violet-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                }`}
              >
                {isConverting ? "Converting..." : "Convert OPUS to MP3"}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8 rounded-r-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p className="text-red-700 font-medium">{error}</p>
              </div>
            </div>
          )}

          {/* Success Message */}
          {convertedFile && (
            <div className="bg-white border border-gray-200 rounded-2xl p-8 mb-8 shadow-lg">
              <div className="text-center">
                {/* Success Icon */}
                <div className="flex justify-center mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>

                {/* Success Title */}
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  🎉 Conversion Successful!
                </h3>
                <p className="text-gray-600 mb-2">Your OPUS file has been converted to MP3</p>
                <p className="text-sm text-gray-500 mb-8">High-quality MP3 file is ready for download</p>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href={convertedFile}
                    download="converted_opus_to_mp3.mp3"
                    className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download MP3 File
                  </a>
                  <button
                    onClick={resetConverter}
                    className="inline-flex items-center justify-center px-8 py-4 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200 border border-gray-200 hover:border-gray-300"
                  >
                    <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Convert Another File
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Progress Section */}
          {isConverting && (
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-violet-100 rounded-full mb-4">
                  <svg className="w-8 h-8 text-violet-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Converting OPUS to MP3...</h3>
                <p className="text-gray-600 mb-6">Please wait while we process your file</p>
                
                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                  <div 
                    className="bg-gradient-to-r from-violet-600 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${conversionProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500">{conversionProgress}% complete</p>
              </div>
            </div>
          )}

          {/* About OPUS Format */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">About OPUS to MP3 Conversion</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">What is OPUS?</h3>
                <p className="text-gray-600 mb-4">
                  OPUS is a modern, open-source audio codec designed for interactive speech and music transmission over the Internet.
                </p>
                <ul className="text-gray-600 space-y-2">
                  <li>• Modern audio codec</li>
                  <li>• Open-source format</li>
                  <li>• High compression efficiency</li>
                  <li>• Used by Discord, WhatsApp</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Why Convert to MP3?</h3>
                <p className="text-gray-600 mb-4">
                  Converting OPUS to MP3 ensures your audio files work on all devices and platforms with universal compatibility.
                </p>
                <ul className="text-gray-600 space-y-2">
                  <li>• Universal compatibility</li>
                  <li>• Works on all devices</li>
                  <li>• Widely supported format</li>
                  <li>• Easy to share and stream</li>
                </ul>
              </div>
            </div>

            {/* Additional SEO Content */}
            <div className="mt-8 pt-8 border-t border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">How to Convert OPUS to MP3 Online</h3>
              <ol className="text-gray-600 space-y-3">
                <li><strong>Step 1:</strong> Click the upload area or drag and drop your OPUS file</li>
                <li><strong>Step 2:</strong> Wait for the file to load (no server upload needed)</li>
                <li><strong>Step 3:</strong> Click &quot;Convert OPUS to MP3&quot; button</li>
                <li><strong>Step 4:</strong> Download your converted MP3 file</li>
              </ol>
              
              <div className="mt-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Common OPUS to MP3 Conversion Scenarios</h4>
                <ul className="text-gray-600 space-y-2">
                  <li>• Converting Discord voice call recordings</li>
                  <li>• Making WhatsApp audio files compatible</li>
                  <li>• Preparing audio for streaming platforms</li>
                  <li>• Converting voice recordings for universal playback</li>
                  <li>• Archiving audio in a more compatible format</li>
                </ul>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-gradient-to-r from-gray-50 to-violet-50 rounded-2xl p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Is OPUS better quality than MP3?</h3>
                <p className="text-gray-600">OPUS generally provides better quality at lower bitrates than MP3. However, MP3 has universal compatibility which is why conversion is useful.</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Where are OPUS files commonly used?</h3>
                <p className="text-gray-600">OPUS is commonly used in Discord voice calls, WhatsApp audio messages, and other modern communication platforms for efficient transmission.</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">What bitrate should I use for OPUS to MP3?</h3>
                <p className="text-gray-600">We recommend 192-320 kbps for music and 128 kbps for speech. Our converter automatically optimizes the bitrate for best quality.</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">How long does OPUS to MP3 conversion take?</h3>
                <p className="text-gray-600">Conversion typically takes just a few seconds for most files, depending on file size and your device&apos;s processing power.</p>
              </div>
            </div>
          </div>

          {/* Related Converters */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Other Audio Converters</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/flac-to-mp3" className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-blue-100">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">FLAC to MP3</h3>
                <p className="text-gray-600 text-sm">Convert lossless FLAC files to MP3</p>
              </Link>
              
              <Link href="/m4a-to-mp3" className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-green-100">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">M4A to MP3</h3>
                <p className="text-gray-600 text-sm">Convert iTunes M4A files to MP3</p>
              </Link>
              
              <Link href="/wma-to-mp3" className="bg-gradient-to-br from-red-50 to-orange-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-red-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-red-200 transition-colors">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">WMA to MP3</h3>
                <p className="text-gray-600 text-sm">Convert Windows Media Audio</p>
              </Link>
              
              <Link href="/wav-to-mp3" className="bg-gradient-to-br from-purple-50 to-indigo-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-purple-100">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">WAV to MP3</h3>
                <p className="text-gray-600 text-sm">Convert uncompressed WAV files</p>
              </Link>
              
              <Link href="/ape-to-mp3" className="bg-gradient-to-br from-rose-50 to-pink-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-rose-100">
                <div className="w-12 h-12 bg-rose-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-rose-200 transition-colors">
                  <svg className="w-6 h-6 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">APE to MP3</h3>
                <p className="text-gray-600 text-sm">Convert Monkey&apos;s Audio files</p>
              </Link>
              
              <Link href="/3gp-to-mp3" className="bg-gradient-to-br from-emerald-50 to-green-50 p-6 rounded-xl hover:shadow-lg transition-all duration-200 text-center group border border-emerald-100">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-emerald-200 transition-colors">
                  <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">3GP to MP3</h3>
                <p className="text-gray-600 text-sm">Convert 3GP videos to MP3</p>
              </Link>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}