import { Metadata } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "OPUS to MP3 Converter - Free Online Convert OPUS Audio to MP3 | ToMP3",
  description: "Convert OPUS to MP3 online for free. Transform OPUS audio files to universal MP3 format with no upload required. High-quality browser-based conversion. 100% secure and private.",
  keywords: "opus to mp3, opus converter, opus audio to mp3, audio converter, free opus converter, online opus to mp3, convert opus files, free opus to mp3 converter",
  openGraph: {
    title: "OPUS to MP3 Converter - Free Online Convert OPUS Audio to MP3",
    description: "Convert OPUS to MP3 online for free. Transform OPUS audio files to universal MP3 format with no upload required. High-quality browser-based conversion.",
    url: `${seoConfig.baseUrl}/opus-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "OPUS to MP3 Converter - Free Online Convert OPUS Audio to MP3"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "OPUS to MP3 Converter - Free Online Convert OPUS Audio to MP3",
    description: "Convert OPUS to MP3 online for free. Transform OPUS audio files to universal MP3 format. High-quality browser-based conversion.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/opus-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function OpusToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}