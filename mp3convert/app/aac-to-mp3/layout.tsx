import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "AAC to MP3 Converter Free Online | Advanced Audio Codec to MP3 | ToMP3",
  description: "Convert AAC to MP3 online for free. Transform Advanced Audio Codec files to universal MP3 format for maximum compatibility. No upload required - 100% secure and private.",
  keywords: "aac to mp3, aac to mp3 converter, convert aac to mp3, aac to mp3 online, free aac to mp3, advanced audio codec, audio converter",
  openGraph: {
    title: "AAC to MP3 Converter Free Online | ToMP3",
    description: "Convert AAC to MP3 online for free. Transform Advanced Audio Codec files to universal MP3 format for maximum compatibility.",
    url: "https://www.tomp3.online/aac-to-mp3",
    type: "website",
  },
  twitter: {
    title: "AAC to MP3 Converter Free Online | ToMP3",
    description: "Convert AAC to MP3 online for free. Advanced Audio Codec to MP3 conversion.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/aac-to-mp3",
  },
};

export default function AacToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
