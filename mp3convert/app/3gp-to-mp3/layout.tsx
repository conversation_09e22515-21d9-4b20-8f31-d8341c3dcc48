import { Metadata } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "3GP to MP3 Converter - Free Online Extract Audio from 3GP Videos | ToMP3",
  description: "Convert 3GP to MP3 online for free. Extract audio from 3GP (3GPP) video files including mobile phone videos. No upload required, browser-based conversion. 100% secure and private.",
  keywords: "3gp to mp3, 3gp converter, 3gpp to mp3, mobile video to mp3, phone video converter, extract audio from 3gp, online 3gp converter, free 3gp to mp3 converter",
  openGraph: {
    title: "3GP to MP3 Converter - Free Online Extract Audio from 3GP Videos",
    description: "Convert 3GP to MP3 online for free. Extract audio from 3GP (3GPP) video files including mobile phone videos. No upload required, browser-based conversion.",
    url: `${seoConfig.baseUrl}/3gp-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "3GP to MP3 Converter - Free Online Extract Audio from 3GP Videos"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "3GP to MP3 Converter - Free Online Extract Audio from 3GP Videos",
    description: "Convert 3GP to MP3 online for free. Extract audio from 3GP (3GPP) video files including mobile phone videos.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/3gp-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function TgpToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}