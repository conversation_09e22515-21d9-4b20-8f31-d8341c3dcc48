import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "FLAC to MP3 Converter Free Online | High Quality Audio Conversion | ToMP3",
  description: "Convert FLAC to MP3 online for free. High-quality FLAC to MP3 conversion with no upload required. Maintain excellent audio quality while reducing file size. 100% secure and private.",
  keywords: "flac to mp3, flac to mp3 converter, convert flac to mp3, flac to mp3 online, free flac to mp3, flac mp3 converter, audio converter, lossless to mp3",
  openGraph: {
    title: "FLAC to MP3 Converter Free Online | ToMP3",
    description: "Convert FLAC to MP3 online for free. High-quality conversion with no upload required. 100% secure and private.",
    url: "https://www.tomp3.online/flac-to-mp3",
    type: "website",
  },
  twitter: {
    title: "FLAC to MP3 Converter Free Online | ToMP3",
    description: "Convert FLAC to MP3 online for free. High-quality conversion with no upload required.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/flac-to-mp3",
  },
};

export default function FlacToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
