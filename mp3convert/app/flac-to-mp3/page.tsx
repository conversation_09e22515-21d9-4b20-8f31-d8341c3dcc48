"use client";

import { useState, useRef } from "react";
import { arrayBufferToBlob, createDownloadUrl } from "@/lib/audioConverter";
import Link from "next/link";

export default function FlacToMp3() {
  // State for file handling
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [conversionProgress, setConversionProgress] = useState<number>(0);
  const [convertedFile, setConvertedFile] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Refs for file inputs
  const audioInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleAudioFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  // Process file (shared between click and drag)
  const processFile = (file: File) => {
    // Check if it's a FLAC file
    if (!file.name.toLowerCase().endsWith('.flac') && !file.type.includes('flac')) {
      setError("Please select a FLAC file (.flac)");
      return;
    }
    setAudioFile(file);
    setError(null);
    setConvertedFile(null);
  };

  // Handle drag events
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  // Handle label click to prevent triggering after drag
  const handleLabelClick = (event: React.MouseEvent<HTMLLabelElement>) => {
    if (isDragging) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  // Convert file function
  const convertFile = async () => {
    if (!audioFile) {
      setError("Please select a FLAC file first");
      return;
    }

    setIsConverting(true);
    setConversionProgress(0);
    setError(null);
    
    try {
      // Create a new Web Worker for audio conversion
      const worker = new Worker(new URL("../../lib/audioConverter.worker.ts", import.meta.url));
      
      // Listen for messages from the worker
      worker.onmessage = (event) => {
        const { type: messageType, result, error, progress } = event.data;

        if (messageType === "progress") {
          setConversionProgress(progress);
        } else if (messageType === "success") {
          // Create download URL
          const blob = arrayBufferToBlob(result, "audio/mpeg");
          const url = createDownloadUrl(blob);
          setConvertedFile(url);
          setIsConverting(false);
          setConversionProgress(100);
        } else if (messageType === "error") {
          setError(error || "Conversion failed");
          setIsConverting(false);
          setConversionProgress(0);
        }
      };

      // Send file to worker
      worker.postMessage({
        type: "audio",
        file: audioFile
      });

    } catch {
      setError("Failed to convert file. Please try again.");
      setIsConverting(false);
    }
  };

  // Reset converter
  const resetConverter = () => {
    setAudioFile(null);
    setConvertedFile(null);
    setError(null);
    setConversionProgress(0);
    setIsConverting(false);
    if (audioInputRef.current) {
      audioInputRef.current.value = "";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                ToMP3
              </span>
            </Link>
            <div className="hidden lg:flex items-center space-x-4">
              <span className="text-blue-600 font-medium text-sm">FLAC</span>
              <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">M4A</Link>
              <Link href="/wav-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">WAV</Link>
              <Link href="/aac-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">AAC</Link>
              <Link href="/mov-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">MOV</Link>
              <Link href="/mp4-to-mp3" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">MP4</Link>
              <Link href="/help" className="text-gray-600 hover:text-blue-600 transition-colors text-sm">Help</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            FLAC to MP3 Converter
            <span className="block bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Free & High Quality
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Convert your <strong>FLAC files to MP3</strong> format online for free. Maintain excellent audio quality while reducing file size by up to 80%.
            <span className="font-semibold text-gray-800">No upload required - 100% secure and private browser-based conversion.</span>
          </p>
          
          {/* Additional SEO-rich content */}
          <div className="mt-6 text-base text-gray-600 max-w-4xl mx-auto">
            <p className="mb-3">
              Our <strong>free FLAC to MP3 converter online</strong> uses advanced audio encoding to preserve the quality of your <strong>lossless FLAC files</strong> while creating smaller, more compatible MP3 files. Perfect for <strong>music library conversion</strong>, sharing audio files, or reducing storage space.
            </p>
            <p>
              Unlike other converters, our tool processes everything locally in your browser using <strong>WebAssembly technology</strong> - no file uploads, no privacy concerns, and no software downloads required.
            </p>
          </div>
        </div>

        {/* Converter Tool */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-12">
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
            <h2 className="text-2xl font-bold mb-2">FLAC to MP3 Converter Tool</h2>
            <p className="text-blue-100">Upload your FLAC file and convert to MP3 instantly</p>
          </div>

          <div className="p-8">
            {/* File Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors mb-6">
              <input
                ref={audioInputRef}
                type="file"
                accept=".flac,audio/flac"
                onChange={handleAudioFileSelect}
                className="hidden"
                id="flac-upload"
              />
              <label htmlFor="flac-upload" className="cursor-pointer">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {audioFile ? audioFile.name : "Drop your FLAC file here"}
                </h3>
                <p className="text-gray-600">or click to browse</p>
                <p className="text-sm text-gray-500 mt-2">Supports FLAC files up to 100MB</p>
              </label>
            </div>

            {/* Convert Button */}
            <button
              onClick={convertFile}
              disabled={!audioFile || isConverting}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 ${
                !audioFile || isConverting
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              }`}
            >
              {isConverting ? "Converting..." : "Convert FLAC to MP3"}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8 rounded-r-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Success Message */}
        {convertedFile && (
          <div className="bg-white border border-gray-200 rounded-2xl p-8 mb-8 shadow-lg">
            <div className="text-center">
              {/* Success Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>

              {/* Success Title */}
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                🎉 Conversion Successful!
              </h3>
              <p className="text-gray-600 mb-2">Your FLAC file has been converted to MP3</p>
              <p className="text-sm text-gray-500 mb-8">High-quality MP3 file is ready for download</p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href={convertedFile}
                  download="converted_flac_to_mp3.mp3"
                  className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Download MP3 File
                </a>
                <button
                  onClick={resetConverter}
                  className="inline-flex items-center justify-center px-8 py-4 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200 border border-gray-200 hover:border-gray-300"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Convert Another File
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Progress Section */}
        {isConverting && (
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-blue-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Converting FLAC to MP3...</h3>
              <p className="text-gray-600 mb-6">Please wait while we process your file</p>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div 
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 h-3 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${conversionProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">{conversionProgress}% complete</p>
            </div>
          </div>
        )}

        {/* Additional Content Sections for SEO */}
        <div className="space-y-12 mt-16">
          
          {/* About FLAC to MP3 Conversion */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">About FLAC to MP3 Conversion</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">What is FLAC?</h3>
                <p className="text-gray-600 mb-4">
                  <strong>FLAC (Free Lossless Audio Codec)</strong> is a high-quality audio format that compresses audio without losing any data. FLAC files maintain the original audio quality but are typically 2-8 times larger than MP3 files.
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Lossless compression maintains original quality</li>
                  <li>Larger file sizes (typically 20-60MB per song)</li>
                  <li>Supported by audiophile equipment</li>
                  <li>Open-source and royalty-free</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Why Convert FLAC to MP3?</h3>
                <p className="text-gray-600 mb-4">
                  While FLAC offers superior quality, <strong>MP3 provides better compatibility</strong> and smaller file sizes. Converting FLAC to MP3 is ideal for:
                </p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Saving storage space (80% size reduction)</li>
                  <li>Better device compatibility</li>
                  <li>Faster file transfers and streaming</li>
                  <li>Email sharing and web uploads</li>
                </ul>
              </div>
            </div>
          </div>

          {/* How-to Guide */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">How to Convert FLAC to MP3 Online</h2>
            <div className="space-y-6">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Select Your FLAC File</h4>
                  <p className="text-gray-600">Click the upload area above or drag and drop your FLAC file. Our converter accepts files up to 100MB for optimal performance.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-green-600 font-bold">2</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Automatic Conversion Starts</h4>
                  <p className="text-gray-600">Once uploaded, the conversion begins immediately using WebAssembly technology. Your file is processed locally in your browser for maximum privacy.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-purple-600 font-bold">3</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Download Your MP3</h4>
                  <p className="text-gray-600">When conversion is complete, download your high-quality MP3 file. The converted file maintains excellent audio quality while being much smaller.</p>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">FLAC to MP3 Converter FAQ</h2>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Is converting FLAC to MP3 free?</h4>
                <p className="text-gray-600">Yes! Our FLAC to MP3 converter is completely free with no limits on the number of files you can convert. No registration or payment required.</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Will I lose audio quality converting FLAC to MP3?</h4>
                <p className="text-gray-600">There will be some quality loss since MP3 uses lossy compression, but our converter uses high-quality encoding settings to minimize this. For most listeners, the difference is barely noticeable.</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">How much smaller will my MP3 file be?</h4>
                <p className="text-gray-600">MP3 files are typically 80-90% smaller than FLAC files. A 50MB FLAC file usually becomes a 5-8MB MP3 file while maintaining good audio quality.</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Can I convert multiple FLAC files at once?</h4>
                <p className="text-gray-600">Currently, our converter processes one file at a time for optimal quality and performance. You can convert multiple files by repeating the process for each file.</p>
              </div>
            </div>
          </div>

          {/* Related Converters */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Other Audio Converters</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link href="/m4a-to-mp3" className="bg-white p-4 rounded-xl text-center hover:shadow-lg transition-all duration-200 group">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-sm">M4A to MP3</h4>
              </Link>
              
              <Link href="/wav-to-mp3" className="bg-white p-4 rounded-xl text-center hover:shadow-lg transition-all duration-200 group">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200 transition-colors">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-sm">WAV to MP3</h4>
              </Link>
              
              <Link href="/aac-to-mp3" className="bg-white p-4 rounded-xl text-center hover:shadow-lg transition-all duration-200 group">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-yellow-200 transition-colors">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-sm">AAC to MP3</h4>
              </Link>
              
              <Link href="/mp4-to-mp3" className="bg-white p-4 rounded-xl text-center hover:shadow-lg transition-all duration-200 group">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors">
                  <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h4 className="font-semibold text-sm">MP4 to MP3</h4>
              </Link>
            </div>
          </div>
          
        </div>
      </main>
    </div>
  );
}
