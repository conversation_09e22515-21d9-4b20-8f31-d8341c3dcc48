import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "WAV to MP3 Converter Free Online | Uncompressed to Compressed Audio | ToMP3",
  description: "Convert WAV to MP3 online for free. Reduce file size by 90% while maintaining good audio quality. No upload required - 100% secure and private WAV to MP3 conversion.",
  keywords: "wav to mp3, wav to mp3 converter, convert wav to mp3, wav to mp3 online, free wav to mp3, uncompressed to compressed, audio converter",
  openGraph: {
    title: "WAV to MP3 Converter Free Online | ToMP3",
    description: "Convert WAV to MP3 online for free. Reduce file size dramatically while maintaining good audio quality.",
    url: "https://www.tomp3.online/wav-to-mp3",
    type: "website",
  },
  twitter: {
    title: "WAV to MP3 Converter Free Online | ToMP3",
    description: "Convert WAV to MP3 online for free. Reduce file size dramatically.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/wav-to-mp3",
  },
};

export default function WavToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
