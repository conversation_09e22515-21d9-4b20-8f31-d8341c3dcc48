import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "MOV to MP3 Converter Free Online | Extract Audio from QuickTime Videos | ToMP3",
  description: "Convert MOV to MP3 online for free. Extract high-quality audio from QuickTime MOV videos. No upload required - 100% secure and private MOV to MP3 conversion.",
  keywords: "mov to mp3, mov to mp3 converter, convert mov to mp3, mov to mp3 online, free mov to mp3, quicktime to mp3, video to mp3, extract audio from mov",
  openGraph: {
    title: "MOV to MP3 Converter Free Online | ToMP3",
    description: "Convert MOV to MP3 online for free. Extract audio from QuickTime videos with no upload required.",
    url: "https://www.tomp3.online/mov-to-mp3",
    type: "website",
  },
  twitter: {
    title: "MOV to MP3 Converter Free Online | ToMP3",
    description: "Convert MOV to MP3 online for free. Extract audio from QuickTime videos.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/mov-to-mp3",
  },
};

export default function MovToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
