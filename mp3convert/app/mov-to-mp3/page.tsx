"use client";

import { useState, useRef } from "react";
import { arrayBufferToBlob, createDownloadUrl } from "@/lib/audioConverter";
import Link from "next/link";

export default function MovToMp3() {
  // State for file handling
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [conversionProgress, setConversionProgress] = useState<number>(0);
  const [convertedFile, setConvertedFile] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Refs for file inputs
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleVideoFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  // Process file (shared between click and drag)
  const processFile = (file: File) => {
    // Check if it's a MOV file
    if (!file.name.toLowerCase().endsWith('.mov') && !file.type.includes('mov')) {
      setError("Please select a MOV file (.mov)");
      return;
    }
    setVideoFile(file);
    setError(null);
    setConvertedFile(null);
  };

  // Handle drag events
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragEnter = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  // Handle label click to prevent triggering after drag
  const handleLabelClick = (event: React.MouseEvent<HTMLLabelElement>) => {
    if (isDragging) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  // Convert file function
  const convertFile = async () => {
    if (!videoFile) {
      setError("Please select a MOV file first");
      return;
    }

    setIsConverting(true);
    setConversionProgress(0);
    setError(null);
    
    try {
      // Create a new Web Worker for video conversion
      const worker = new Worker(new URL("../../lib/audioConverter.worker.ts", import.meta.url));
      
      // Listen for messages from the worker
      worker.onmessage = (event) => {
        const { type: messageType, result, error, progress } = event.data;

        if (messageType === "progress") {
          setConversionProgress(progress);
        } else if (messageType === "success") {
          // Create download URL
          const blob = arrayBufferToBlob(result, "audio/mpeg");
          const url = createDownloadUrl(blob);
          setConvertedFile(url);
          setIsConverting(false);
          setConversionProgress(100);
        } else if (messageType === "error") {
          setError(error || "Conversion failed");
          setIsConverting(false);
          setConversionProgress(0);
        }
      };

      // Send file to worker
      worker.postMessage({
        type: "video",
        file: videoFile
      });

    } catch {
      setError("Failed to convert file. Please try again.");
      setIsConverting(false);
    }
  };

  // Reset converter
  const resetConverter = () => {
    setVideoFile(null);
    setConvertedFile(null);
    setError(null);
    setConversionProgress(0);
    setIsConverting(false);
    if (videoInputRef.current) {
      videoInputRef.current.value = "";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-pink-50">
      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                ToMP3
              </span>
            </Link>
            <div className="hidden sm:flex items-center space-x-6">
              <Link href="/flac-to-mp3" className="text-gray-600 hover:text-purple-600 transition-colors">FLAC to MP3</Link>
              <Link href="/m4a-to-mp3" className="text-gray-600 hover:text-purple-600 transition-colors">M4A to MP3</Link>
              <Link href="/mp4-to-mp3" className="text-gray-600 hover:text-purple-600 transition-colors">MP4 to MP3</Link>
              <Link href="/help" className="text-gray-600 hover:text-purple-600 transition-colors">Help</Link>
              <Link href="/" className="text-gray-600 hover:text-purple-600 transition-colors">All Converters</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            MOV to MP3 Converter
            <span className="block bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Extract Audio from Videos
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Convert your <strong>MOV videos to MP3</strong> audio format online for free. Extract high-quality audio from QuickTime MOV files. 
            <span className="font-semibold text-gray-800">No upload required - 100% secure and private.</span>
          </p>
        </div>

        {/* Converter Tool */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-12">
          <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-6 text-white">
            <h2 className="text-2xl font-bold mb-2">MOV to MP3 Converter Tool</h2>
            <p className="text-purple-100">Upload your MOV video and extract audio as MP3</p>
          </div>

          <div className="p-8">
            {/* File Upload Area */}
            <div
              className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-purple-400 transition-colors mb-6"
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input
                ref={videoInputRef}
                type="file"
                accept=".mov,video/quicktime"
                onChange={handleVideoFileSelect}
                className="hidden"
                id="mov-upload"
              />
              <label htmlFor="mov-upload" className="cursor-pointer" onClick={handleLabelClick}>
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {videoFile ? videoFile.name : "Drop your MOV video here"}
                </h3>
                <p className="text-gray-600">or click to browse</p>
                <p className="text-sm text-gray-500 mt-2">Supports MOV files from QuickTime and other sources</p>
              </label>
            </div>

            {/* Convert Button */}
            <button
              onClick={convertFile}
              disabled={!videoFile || isConverting}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 ${
                !videoFile || isConverting
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              }`}
            >
              {isConverting ? "Extracting Audio..." : "Convert MOV to MP3"}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-8 rounded-r-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {/* Success Message */}
        {convertedFile && (
          <div className="bg-white border border-gray-200 rounded-2xl p-8 mb-8 shadow-lg">
            <div className="text-center">
              {/* Success Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2.5" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>

              {/* Success Title */}
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                🎉 Conversion Successful!
              </h3>
              <p className="text-gray-600 mb-2">Your MOV file has been converted to MP3</p>
              <p className="text-sm text-gray-500 mb-8">High-quality MP3 file is ready for download</p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href={convertedFile}
                  download="converted_mov_to_mp3.mp3"
                  className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Download MP3 File
                </a>
                <button
                  onClick={resetConverter}
                  className="inline-flex items-center justify-center px-8 py-4 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200 border border-gray-200 hover:border-gray-300"
                >
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Convert Another File
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Progress Section */}
        {isConverting && (
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-12">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
                <svg className="w-8 h-8 text-purple-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Extracting Audio from MOV...</h3>
              <p className="text-gray-600 mb-6">Please wait while we extract audio from your video</p>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div 
                  className="bg-gradient-to-r from-purple-600 to-pink-600 h-3 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${conversionProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">{conversionProgress}% complete</p>
            </div>
          </div>
        )}

        {/* About MOV Format */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">About MOV to MP3 Conversion</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">What is MOV?</h3>
              <p className="text-gray-600 mb-4">
                MOV is a video file format developed by Apple for QuickTime. It can contain video, audio, and text tracks in a single file.
              </p>
              <ul className="text-gray-600 space-y-2">
                <li>• Apple QuickTime format</li>
                <li>• High quality video and audio</li>
                <li>• Large file sizes</li>
                <li>• Common on Mac devices</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Why Extract Audio as MP3?</h3>
              <p className="text-gray-600 mb-4">
                Extracting audio from MOV videos as MP3 creates smaller, more portable audio files that work on all devices.
              </p>
              <ul className="text-gray-600 space-y-2">
                <li>• Much smaller file sizes</li>
                <li>• Universal audio compatibility</li>
                <li>• Perfect for music and podcasts</li>
                <li>• Easy to share and stream</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Related Converters */}
        <div className="bg-gradient-to-r from-gray-50 to-purple-50 rounded-2xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Other Video to Audio Converters</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link href="/mp4-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-orange-200 transition-colors">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">MP4 to MP3</h3>
              <p className="text-gray-600 text-sm">Extract audio from MP4 videos</p>
            </Link>
            
            <Link href="/flac-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">FLAC to MP3</h3>
              <p className="text-gray-600 text-sm">Convert lossless FLAC to MP3</p>
            </Link>
            
            <Link href="/m4a-to-mp3" className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 text-center group">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">M4A to MP3</h3>
              <p className="text-gray-600 text-sm">Convert iTunes M4A files to MP3</p>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
