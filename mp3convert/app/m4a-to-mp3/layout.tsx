import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "M4A to MP3 Converter Free Online | iTunes Compatible | ToMP3",
  description: "Convert M4A to MP3 online for free. Perfect for iTunes music files and AAC audio. High-quality M4A to MP3 conversion with no upload required. 100% secure and private.",
  keywords: "m4a to mp3, m4a to mp3 converter, convert m4a to mp3, m4a to mp3 online, free m4a to mp3, itunes to mp3, aac to mp3, audio converter",
  openGraph: {
    title: "M4A to MP3 Converter Free Online | ToMP3",
    description: "Convert M4A to MP3 online for free. Perfect for iTunes music files. High-quality conversion with no upload required.",
    url: "https://www.tomp3.online/m4a-to-mp3",
    type: "website",
  },
  twitter: {
    title: "M4A to MP3 Converter Free Online | ToMP3",
    description: "Convert M4A to MP3 online for free. Perfect for iTunes music files.",
  },
  alternates: {
    canonical: "https://www.tomp3.online/m4a-to-mp3",
  },
};

export default function M4aToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
