import { Metada<PERSON> } from "next";
import seoConfig from "@/seo.config";

export const metadata: Metadata = {
  title: "APE to MP3 Converter - Free Online Convert Monkey's Audio to MP3 | ToMP3",
  description: "Convert APE to MP3 online for free. Transform Monkey's Audio files to universal MP3 format with no upload required. High-quality browser-based conversion. 100% secure and private.",
  keywords: "ape to mp3, ape converter, monkeys audio to mp3, lossless audio converter, audio converter, free ape converter, online ape to mp3, convert ape files, free ape to mp3 converter",
  openGraph: {
    title: "APE to MP3 Converter - Free Online Convert Monkey's Audio to MP3",
    description: "Convert APE to MP3 online for free. Transform Monkey's Audio files to universal MP3 format with no upload required. High-quality browser-based conversion.",
    url: `${seoConfig.baseUrl}/ape-to-mp3`,
    siteName: seoConfig.siteName,
    images: [
      {
        url: `${seoConfig.baseUrl}${seoConfig.ogImage}`,
        width: 1200,
        height: 630,
        alt: "APE to MP3 Converter - Free Online Convert Monkey's Audio to MP3"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "APE to MP3 Converter - Free Online Convert Monkey's Audio to MP3",
    description: "Convert APE to MP3 online for free. Transform Monkey's Audio files to universal MP3 format. High-quality browser-based conversion.",
    images: [`${seoConfig.baseUrl}${seoConfig.ogImage}`],
    creator: seoConfig.twitterHandle
  },
  alternates: {
    canonical: `${seoConfig.baseUrl}/ape-to-mp3`
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function ApeToMp3Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}