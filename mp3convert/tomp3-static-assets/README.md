# 🎵 ToMP3 Static Assets

This repository hosts static assets for the ToMP3 audio converter project.

## 📁 Structure

```
├── images/
│   ├── og-image.jpg     # OpenGraph image (1200x630)
│   └── og-image.svg     # SVG source
├── icons/
│   ├── favicon.ico      # Website favicon
│   ├── favicon.svg      # SVG favicon
│   └── apple-touch-icon.png # Apple touch icon
└── index.html           # Asset browser
```

## 🔗 CDN URLs

### OpenGraph Image
```
https://yourusername.github.io/tomp3-static/images/og-image.jpg
```

### Icons
```
https://yourusername.github.io/tomp3-static/icons/favicon.ico
https://yourusername.github.io/tomp3-static/icons/favicon.svg
https://yourusername.github.io/tomp3-static/icons/apple-touch-icon.png
```

## 🚀 Setup Instructions

1. Create a new public repository named `tomp3-static`
2. Upload these files to the repository
3. Enable GitHub Pages in repository settings
4. Update your main project's meta tags with the new URLs

## 📱 Social Media Integration

Use these URLs in your main project's `layout.tsx`:

```typescript
openGraph: {
  images: [
    {
      url: "https://yourusername.github.io/tomp3-static/images/og-image.jpg",
      width: 1200,
      height: 630,
      alt: "ToMP3 - Free Online Audio & Video to MP3 Converter",
    },
  ],
},
twitter: {
  images: ["https://yourusername.github.io/tomp3-static/images/og-image.jpg"],
},
```

## 🔄 Updates

To update assets:
1. Replace files in this repository
2. Commit and push changes
3. GitHub Pages will automatically update
4. Clear social media cache using debugging tools

---

**Note**: Replace `yourusername` with your actual GitHub username in all URLs.
