<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" stroke="url(#strokeGradient)" stroke-width="2"/>
  
  <!-- Audio wave symbol -->
  <g transform="translate(8, 10)">
    <!-- Sound waves -->
    <rect x="2" y="6" width="2" height="4" rx="1" fill="white"/>
    <rect x="5" y="4" width="2" height="8" rx="1" fill="white"/>
    <rect x="8" y="2" width="2" height="12" rx="1" fill="white"/>
    <rect x="11" y="5" width="2" height="6" rx="1" fill="white"/>
    <rect x="14" y="7" width="2" height="2" rx="1" fill="white"/>
  </g>
  
  <!-- MP3 indicator -->
  <circle cx="24" cy="8" r="3" fill="#EF4444"/>
  <text x="24" y="10" font-family="Arial, sans-serif" font-size="4" font-weight="bold" text-anchor="middle" fill="white">3</text>
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="strokeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
