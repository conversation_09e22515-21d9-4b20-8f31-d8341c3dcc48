<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToMP3 Static Assets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .asset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .asset-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .asset-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 10px 0;
        }
        .url-box {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
        .copy-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 ToMP3 Static Assets</h1>
        <p>This repository hosts static assets for the ToMP3 audio converter project.</p>
        
        <div class="asset-grid">
            <div class="asset-card">
                <h3>OpenGraph Image</h3>
                <img src="images/og-image.jpg" alt="OG Image" class="asset-image">
                <div class="url-box" id="og-url">
                    https://yourusername.github.io/tomp3-static/images/og-image.jpg
                </div>
                <button class="copy-btn" onclick="copyUrl('og-url')">Copy URL</button>
                <p><small>1200x630px - Perfect for social media sharing</small></p>
            </div>
        </div>
        
        <h2>📋 Usage Instructions</h2>
        <ol>
            <li>Replace "yourusername" in URLs with your actual GitHub username</li>
            <li>Update your main project's meta tags to use these URLs</li>
            <li>Test with social media debugging tools</li>
        </ol>
        
        <h2>🔗 Quick Links</h2>
        <ul>
            <li><a href="https://developers.facebook.com/tools/debug/" target="_blank">Facebook Debugger</a></li>
            <li><a href="https://cards-dev.twitter.com/validator" target="_blank">Twitter Card Validator</a></li>
            <li><a href="https://www.linkedin.com/post-inspector/" target="_blank">LinkedIn Post Inspector</a></li>
        </ul>
    </div>
    
    <script>
        function copyUrl(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('URL copied to clipboard!');
            });
        }
        
        // 自动替换用户名（如果在GitHub Pages上）
        if (window.location.hostname.includes('github.io')) {
            const username = window.location.hostname.split('.')[0];
            const repoName = window.location.pathname.split('/')[1];
            document.getElementById('og-url').textContent = 
                `https://${username}.github.io/${repoName}/images/og-image.jpg`;
        }
    </script>
</body>
</html>
