import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile } from '@ffmpeg/util';

// Initialize FFmpeg
let ffmpeg: FFmpeg | null = null;

const initFFmpeg = async () => {
  if (!ffmpeg) {
    ffmpeg = new FFmpeg();
    await ffmpeg.load();
  }
  return ffmpeg;
};

// Generic audio to MP3 conversion function using FFmpeg.js
export const convertAudioToMp3 = async (audioFile: File, options?: {
  bitrate?: string;
  sampleRate?: string;
  channels?: string;
}): Promise<ArrayBuffer> => {
  try {
    // Initialize FFmpeg
    const ffmpeg = await initFFmpeg();

    // Get file extension from the original file
    const fileExtension = audioFile.name.split('.').pop()?.toLowerCase() || 'audio';

    // Generate unique file names
    const inputFileName = `input_${Date.now()}.${fileExtension}`;
    const outputFileName = `output_${Date.now()}.mp3`;

    // Write input file to FFmpeg's virtual file system
    await ffmpeg.writeFile(inputFileName, await fetchFile(audioFile));

    // Set default options
    const bitrate = options?.bitrate || '128k';
    const sampleRate = options?.sampleRate || '44100';
    const channels = options?.channels || '2';

    // Convert audio to MP3 using FFmpeg
    await ffmpeg.exec([
      '-i', inputFileName,
      '-codec:a', 'libmp3lame',
      '-b:a', bitrate,
      '-ar', sampleRate,
      '-ac', channels,
      outputFileName
    ]);

    // Read the output file
    const data = await ffmpeg.readFile(outputFileName);

    // Clean up temporary files
    try {
      await ffmpeg.deleteFile(inputFileName);
      await ffmpeg.deleteFile(outputFileName);
    } catch (cleanupError) {
      console.warn('Failed to clean up temporary files:', cleanupError);
    }

    // Convert Uint8Array to ArrayBuffer
    const uint8Array = data as Uint8Array;
    return uint8Array.buffer.slice(uint8Array.byteOffset, uint8Array.byteOffset + uint8Array.byteLength) as ArrayBuffer;
  } catch (error) {
    console.error(`Error converting ${audioFile.name} to MP3:`, error);
    throw new Error(`Failed to convert ${audioFile.name} to MP3: ` + (error instanceof Error ? error.message : "Unknown error"));
  }
};

// FLAC to MP3 conversion function (wrapper for backward compatibility)
export const convertFlacToMp3 = async (flacFile: File): Promise<ArrayBuffer> => {
  return convertAudioToMp3(flacFile);
};

// WAV to MP3 conversion function
export const convertWavToMp3 = async (wavFile: File): Promise<ArrayBuffer> => {
  return convertAudioToMp3(wavFile);
};

// AAC to MP3 conversion function
export const convertAacToMp3 = async (aacFile: File): Promise<ArrayBuffer> => {
  return convertAudioToMp3(aacFile);
};

// OGG to MP3 conversion function
export const convertOggToMp3 = async (oggFile: File): Promise<ArrayBuffer> => {
  return convertAudioToMp3(oggFile);
};

// M4A to MP3 conversion function
export const convertM4aToMp3 = async (m4aFile: File): Promise<ArrayBuffer> => {
  return convertAudioToMp3(m4aFile);
};

// WMA to MP3 conversion function
export const convertWmaToMp3 = async (wmaFile: File): Promise<ArrayBuffer> => {
  return convertAudioToMp3(wmaFile);
};

// MOV to MP3 conversion function
export const convertMovToMp3 = async (movFile: File): Promise<ArrayBuffer> => {
  try {
    // Initialize FFmpeg
    const ffmpegInstance = await initFFmpeg();
    
    // Write the input file to FFmpeg's filesystem
    await ffmpegInstance.writeFile('input.mov', await fetchFile(movFile));
    
    // Run FFmpeg command to extract audio and convert to MP3
    await ffmpegInstance.exec(['-i', 'input.mov', '-vn', '-ar', '44100', '-ac', '2', '-ab', '192k', '-f', 'mp3', 'output.mp3']);
    
    // Read the output file
    const mp3Data = await ffmpegInstance.readFile('output.mp3');
    
    // Check if mp3Data is Uint8Array and convert to ArrayBuffer
    if (mp3Data instanceof Uint8Array) {
      // Create a new ArrayBuffer with the same content to ensure correct type
      const buffer = new ArrayBuffer(mp3Data.length);
      const view = new Uint8Array(buffer);
      view.set(mp3Data);
      return buffer;
    } else {
      throw new Error("Unexpected data type returned from FFmpeg readFile");
    }
  } catch (error) {
    console.error("Error converting MOV to MP3:", error);
    throw new Error("Failed to convert MOV to MP3");
  }
};

// Utility function to convert ArrayBuffer to Blob
export const arrayBufferToBlob = (arrayBuffer: ArrayBuffer, mimeType: string): Blob => {
  return new Blob([arrayBuffer], { type: mimeType });
};

// Utility function to create download URL
export const createDownloadUrl = (blob: Blob): string => {
  return URL.createObjectURL(blob);
};