// This is a Web Worker script for audio conversion
// It will run in a separate thread to avoid blocking the main UI thread

import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile } from '@ffmpeg/util';

// Initialize FFmpeg
let ffmpeg: FFmpeg | null = null;
let lastReportedProgress = 0;

const initFFmpeg = async () => {
  if (!ffmpeg) {
    ffmpeg = new FFmpeg();

    // Set up progress listener with smoothing
    ffmpeg.on('progress', ({ progress }) => {
      // Convert to percentage and ensure it's between 0-100
      const progressPercent = Math.max(0, Math.min(100, Math.round(progress * 100)));

      // Only report progress if it has increased (prevent jumping backwards)
      if (progressPercent > lastReportedProgress) {
        lastReportedProgress = progressPercent;
        self.postMessage({
          type: "progress",
          progress: progressPercent
        });
      }
    });

    // Set up log listener for debugging
    ffmpeg.on('log', ({ type, message }) => {
      console.log(`FFmpeg ${type}:`, message);
    });

    await ffmpeg.load();
  }
  return ffmpeg;
};

// Generic audio to MP3 conversion function
const convertAudioToMp3 = async (audioFile: File): Promise<ArrayBuffer> => {
  // Reset progress counter for new conversion
  lastReportedProgress = 0;

  const ffmpeg = await initFFmpeg();

  const fileExtension = audioFile.name.split('.').pop()?.toLowerCase() || 'audio';
  const inputFileName = `input_${Date.now()}.${fileExtension}`;
  const outputFileName = `output_${Date.now()}.mp3`;

  await ffmpeg.writeFile(inputFileName, await fetchFile(audioFile));

  await ffmpeg.exec([
    '-i', inputFileName,
    '-codec:a', 'libmp3lame',
    '-b:a', '128k',
    '-ar', '44100',
    '-ac', '2',
    outputFileName
  ]);

  const data = await ffmpeg.readFile(outputFileName);

  try {
    await ffmpeg.deleteFile(inputFileName);
    await ffmpeg.deleteFile(outputFileName);
  } catch (cleanupError) {
    console.warn('Failed to clean up temporary files:', cleanupError);
  }

  const uint8Array = data as Uint8Array;
  return uint8Array.buffer.slice(uint8Array.byteOffset, uint8Array.byteOffset + uint8Array.byteLength) as ArrayBuffer;
};

// Video to MP3 conversion function
const convertVideoToMp3 = async (videoFile: File): Promise<ArrayBuffer> => {
  try {
    // Reset progress counter for new conversion
    lastReportedProgress = 0;

    const ffmpeg = await initFFmpeg();

    // Get file extension for input filename
    const fileExtension = videoFile.name.split('.').pop()?.toLowerCase() || 'video';
    const inputFileName = `input_${Date.now()}.${fileExtension}`;
    const outputFileName = `output_${Date.now()}.mp3`;

    console.log(`Converting video file: ${videoFile.name} (${Math.round(videoFile.size / 1024 / 1024)}MB)`);

    // Write file and check if successful
    console.log('Writing input file to FFmpeg filesystem...');
    await ffmpeg.writeFile(inputFileName, await fetchFile(videoFile));
    console.log('Input file written successfully');

    // Try conversion with simple parameters
    console.log('Starting FFmpeg conversion...');
    try {
      await ffmpeg.exec([
        '-i', inputFileName,
        '-vn',                    // No video stream
        '-acodec', 'libmp3lame',  // Audio codec
        '-ab', '192k',           // Audio bitrate
        '-ar', '44100',          // Sample rate
        '-ac', '2',              // Stereo channels
        '-y',                    // Overwrite output file
        outputFileName
      ]);
      console.log('FFmpeg conversion completed');
    } catch (execError) {
      console.error('FFmpeg exec error:', execError);
      throw new Error('FFmpeg conversion failed - the video file may not contain audio tracks');
    }

    console.log('Reading output file...');
    let data;
    try {
      data = await ffmpeg.readFile(outputFileName);
      console.log(`Output file read successfully, size: ${data.length} bytes`);
    } catch (readError) {
      console.error('Failed to read output file:', readError);
      throw new Error('Conversion failed: Output file was not created. This usually means the video file has no audio tracks to extract.');
    }

    // Clean up temporary files
    try {
      await ffmpeg.deleteFile(inputFileName);
      await ffmpeg.deleteFile(outputFileName);
    } catch (cleanupError) {
      console.warn('Failed to clean up temporary files:', cleanupError);
    }

    console.log('MOV conversion completed successfully');
    const uint8Array = data as Uint8Array;
    return uint8Array.buffer.slice(uint8Array.byteOffset, uint8Array.byteOffset + uint8Array.byteLength) as ArrayBuffer;
  } catch (error) {
    console.error('MOV conversion error:', error);
    throw new Error(`MOV conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Listen for messages from the main thread
self.onmessage = async (event) => {
  const { type, file } = event.data;

  // Validate input data
  if (!file) {
    console.error('Worker received invalid data: file is undefined');
    self.postMessage({ type: "error", error: "No file provided for conversion" });
    return;
  }

  if (!file.name) {
    console.error('Worker received invalid file: name property is missing');
    self.postMessage({ type: "error", error: "Invalid file: missing name property" });
    return;
  }

  console.log(`Worker received ${type} conversion request for file: ${file.name}`);

  try {
    let result: ArrayBuffer;

    // Perform the conversion based on file type
    if (type === "audio") {
      console.log('Starting audio conversion...');
      result = await convertAudioToMp3(file);
    } else if (type === "video") {
      console.log('Starting video conversion...');
      result = await convertVideoToMp3(file);
    } else {
      throw new Error("Unsupported file type");
    }

    console.log('Conversion completed, sending result back to main thread');
    // Send the result back to the main thread
    self.postMessage({ type: "success", result });
  } catch (error) {
    // Send detailed error back to the main thread
    console.error('Worker conversion error:', error);
    let errorMessage = "Unknown error occurred";

    if (error instanceof Error) {
      errorMessage = error.message;
      console.log('Error message:', errorMessage);

      // Add more context for common errors
      if (errorMessage.includes('ENOENT')) {
        errorMessage = "File not found or could not be read";
      } else if (errorMessage.includes('codec')) {
        errorMessage = "Unsupported video/audio codec in the file";
      } else if (errorMessage.includes('Invalid data')) {
        errorMessage = "Invalid or corrupted file format";
      } else if (errorMessage.includes('does not contain any stream') ||
                 errorMessage.includes('No audio') ||
                 errorMessage.includes('Output file was not created')) {
        errorMessage = "This video file does not contain any audio tracks to extract. Please select a video file with audio.";
      }
    }

    console.log('Sending error to main thread:', errorMessage);
    self.postMessage({ type: "error", error: errorMessage });
  }
};

export {};