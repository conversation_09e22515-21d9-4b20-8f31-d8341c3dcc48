import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable static export for EdgeOne deployment
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
  },

  // Remove experimental.esmExternals for Turbopack compatibility
  // experimental: {
  //   esmExternals: 'loose',
  // },

  // Note: headers() and redirects() are not supported with output: 'export'
  // These will be handled by the deployment platform (EdgeOne/Vercel)

  webpack: (config, { isServer }) => {
    // Client-side configuration
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        stream: false,
        buffer: false,
      };

      // Enable WebAssembly
      config.experiments = {
        ...config.experiments,
        asyncWebAssembly: true,
        layers: true,
      };

      // Handle .wasm files
      config.module.rules.push({
        test: /\.wasm$/,
        type: 'webassembly/async',
      });
    }

    return config;
  },
};

export default nextConfig;
