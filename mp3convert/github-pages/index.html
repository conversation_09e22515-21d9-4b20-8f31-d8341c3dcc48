<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToMP3 - Free Audio Converter | Convert FLAC, M4A, WAV to MP3</title>
    <meta name="description" content="Convert audio files to MP3 format for free. Support FLAC, M4A, WAV, AAC, OGG, MOV, MP4. No uploads, completely private, unlimited file sizes.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .hero {
            text-align: center;
            padding: 60px 0;
            color: white;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: transform 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .features {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: #333;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .feature h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        
        .formats {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 60px 40px;
            margin: 40px 0;
        }
        
        .formats h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #333;
        }
        
        .format-list {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        
        .format-tag {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
        }
        
        .arrow {
            font-size: 2rem;
            color: #667eea;
            margin: 0 20px;
        }
        
        .footer {
            text-align: center;
            padding: 40px 0;
            color: white;
        }
        
        .footer a {
            color: #ff6b6b;
            text-decoration: none;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }
            
            .features, .formats {
                padding: 40px 20px;
            }
            
            .arrow {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <section class="hero">
            <h1>🎵 ToMP3</h1>
            <p>Free Browser-Based Audio Converter</p>
            <p>Convert FLAC, M4A, WAV, AAC, OGG, MOV, MP4 to MP3</p>
            <a href="https://www.tomp3.online" class="cta-button">Start Converting Now →</a>
        </section>

        <section class="features">
            <h2>Why Choose ToMP3?</h2>
            <div class="feature-grid">
                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h3>100% Private</h3>
                    <p>All processing happens in your browser. Your files never leave your device, ensuring complete privacy and security.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>Lightning Fast</h3>
                    <p>WebAssembly-powered FFmpeg delivers desktop-grade performance directly in your browser.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🆓</div>
                    <h3>Completely Free</h3>
                    <p>No registration, no limits, no hidden costs. Convert unlimited files of any size, forever.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <h3>Works Everywhere</h3>
                    <p>Compatible with desktop, tablet, and mobile devices. Works offline after initial load.</p>
                </div>
            </div>
        </section>

        <section class="formats">
            <h2>Supported Formats</h2>
            <div style="text-align: center;">
                <div class="format-list">
                    <span class="format-tag">FLAC</span>
                    <span class="format-tag">M4A</span>
                    <span class="format-tag">WAV</span>
                    <span class="format-tag">AAC</span>
                    <span class="format-tag">OGG</span>
                    <span class="format-tag">MOV</span>
                    <span class="format-tag">MP4</span>
                </div>
                <div class="arrow">↓</div>
                <div class="format-list">
                    <span class="format-tag" style="background: #ff6b6b;">MP3</span>
                </div>
            </div>
            <p style="text-align: center; margin-top: 30px;">
                <a href="https://www.tomp3.online" class="cta-button">Try ToMP3 Now</a>
            </p>
        </section>

        <section class="features">
            <h2>Perfect For</h2>
            <div class="feature-grid">
                <div class="feature">
                    <div class="feature-icon">🎤</div>
                    <h3>Musicians</h3>
                    <p>Convert high-quality FLAC files to MP3 for distribution and sharing while maintaining audio quality.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🎙️</div>
                    <h3>Podcasters</h3>
                    <p>Prepare audio content for hosting platforms by converting various formats to optimized MP3 files.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🎬</div>
                    <h3>Content Creators</h3>
                    <p>Extract audio from video files (MOV, MP4) and convert to MP3 for podcasts or music tracks.</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">👥</div>
                    <h3>Everyone</h3>
                    <p>Anyone who values privacy and needs reliable, free audio conversion without uploading files to servers.</p>
                </div>
            </div>
        </section>
    </div>

    <footer class="footer">
        <p>
            🎵 <strong><a href="https://www.tomp3.online">www.tomp3.online</a></strong> - Free Audio Converter
        </p>
        <p>Convert your audio files privately and securely</p>
    </footer>

    <script>
        // Add some interactivity
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Track clicks if needed
                console.log('CTA clicked:', this.href);
            });
        });
    </script>
</body>
</html>
