<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with rounded corners for iOS -->
  <rect width="180" height="180" rx="40" fill="url(#gradient)"/>
  
  <!-- Audio wave visualization -->
  <g transform="translate(40, 60)">
    <!-- Sound waves -->
    <rect x="10" y="30" width="8" height="20" rx="4" fill="white" opacity="0.9"/>
    <rect x="22" y="20" width="8" height="40" rx="4" fill="white"/>
    <rect x="34" y="10" width="8" height="60" rx="4" fill="white"/>
    <rect x="46" y="25" width="8" height="30" rx="4" fill="white" opacity="0.9"/>
    <rect x="58" y="35" width="8" height="10" rx="4" fill="white" opacity="0.8"/>
    <rect x="70" y="15" width="8" height="50" rx="4" fill="white" opacity="0.9"/>
    <rect x="82" y="25" width="8" height="30" rx="4" fill="white" opacity="0.8"/>
  </g>
  
  <!-- MP3 badge -->
  <circle cx="140" cy="40" r="18" fill="#EF4444" stroke="white" stroke-width="3"/>
  <text x="140" y="48" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">MP3</text>
  
  <!-- ToMP3 branding -->
  <text x="90" y="150" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">ToMP3</text>
  <text x="90" y="170" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white" opacity="0.8">Audio Converter</text>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
